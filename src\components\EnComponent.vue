<template>
  <div class="space-y-6" v-loading="loading">
    <div class="card-container">
      <!-- 搜索输入框 -->
      <div class="relative">
        <el-input
          v-model="searchKeyword"
          type="text"
          placeholder="请输入搜索内容"
          class="!rounded-lg"
          size="large"
          @keydown="handleKeyDown">
          <template #prefix>
            <el-icon>
              <Search />
            </el-icon>
          </template>
        </el-input>
      </div>

      <!-- 搜索选项 -->
      <div class="flex items-center space-x-4 mt-4">
        <div class="w-[200px]">
          <el-select v-model="searchFilter" placeholder="时间筛选" size="large">
            <el-option label="不限" value="noLimit" />
            <el-option label="一天内" value="oneDay" />
            <el-option label="一周内" value="oneWeek" />
            <el-option label="一个月内" value="oneMonth" />
            <el-option label="一年内" value="oneYear" />
          </el-select>
        </div>
        <el-button type="primary" size="large" @click="handleSearch" :loading="loading">
          搜索
        </el-button>
      </div>
    </div>

    <!-- 搜索结果 -->
    <div v-if="searchResults.length > 0" class="card-container">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-semibold">搜索结果</h3>
        <span class="text-gray-500">共找到 {{ total }} 条结果</span>
      </div>

      <div class="space-y-4">
        <div v-for="(result, index) in searchResults" :key="index"
             class="border-b border-gray-200 pb-4 last:border-b-0">
          <h4 class="text-blue-600 hover:text-blue-800 cursor-pointer text-lg font-medium mb-2">
            <a :href="result.url" target="_blank" rel="noopener noreferrer">
              {{ result.title }}
            </a>
          </h4>
          <p class="text-gray-600 text-sm mb-2">{{ result.content }}</p>
          <div class="flex items-center text-sm text-gray-500 space-x-4">
            <span>{{ result.source }}</span>
            <span>{{ result.date }}</span>
            <a :href="result.url" target="_blank" rel="noopener noreferrer"
               class="text-blue-500 hover:text-blue-700">
              {{ result.url }}
            </a>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="flex justify-center mt-6" v-if="total > 10">
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="10"
          :total="total"
          layout="prev, pager, next"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 无结果提示 -->
    <div v-else-if="!loading && searchKeyword && hasSearched" class="card-container text-center py-8">
      <p class="text-gray-500">未找到相关结果，请尝试其他关键词</p>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Search } from "@element-plus/icons-vue";
import { ref } from "vue";
import axios from "axios";
import { ElMessage } from 'element-plus';

// 添加防抖函数
const debounce = (fn: Function, delay: number) => {
  let timer: number | undefined = undefined;
  return (...args: any[]) => {
    if (timer) window.clearTimeout(timer);
    timer = window.setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  };
};

interface SearchResult {
  title: string;
  url: string;
  source: string;
  date: string;
  content: string;
}

interface APIWebPage {
  name?: string;
  title?: string;
  url?: string;
  siteName?: string;
  datePublished?: string;
  date?: string;
  publishedDate?: string;
  snippet?: string;
  description?: string;
  content?: string;
}

interface APIWebPages {
  totalEstimatedMatches?: number;
  value?: APIWebPage[];
}

interface APIResponse {
  webPages?: APIWebPages;
  data?: {
    webPages?: APIWebPages;
  };
}

// 响应式数据
const searchKeyword = ref('');
const searchFilter = ref('noLimit');
const searchResults = ref<SearchResult[]>([]);
const loading = ref(false);
const total = ref(0);
const currentPage = ref(1);
const hasSearched = ref(false);

// 格式化日期
const formatDate = (dateStr: string): string => {
  if (!dateStr) return '';
  try {
    const date = new Date(dateStr);
    return date.toLocaleDateString('zh-CN');
  } catch {
    return dateStr;
  }
};

// API请求函数
async function searchAPI(query: string): Promise<APIResponse> {
  try {
    const response = await axios.post<APIResponse>('https://api.langsearch.com/v1/web-search',
      {
        query,
        freshness: searchFilter.value,
        count: 10
      },
      {
        headers: {
          'Authorization': 'sk-e0bfdd2a6e6148e2a0f64826ac7b8e59',
          'Content-Type': 'application/json'
        }
      }
    );
    return response.data;
  } catch (error) {
    console.error('API请求失败:', error);
    throw error;
  }
}

// 单项搜索处理
const handleSingleSearch = async () => {
  if (!searchKeyword.value.trim()) {
    ElMessage.warning('请输入搜索内容');
    return;
  }

  try {
    loading.value = true;
    hasSearched.value = true;
    currentPage.value = 1;

    const data = await searchAPI(searchKeyword.value);
    const webPages = data.webPages || data.data?.webPages;
    const result = webPages?.value || [];
    total.value = webPages?.totalEstimatedMatches || 0;

    const mappedResult = result.map(item => ({
      title: item.name || item.title || '',
      url: item.url || '',
      source: item.siteName || '',
      date: formatDate(item.datePublished || item.date || item.publishedDate || ''),
      content: item.snippet || item.description || item.content || ''
    }));

    searchResults.value = mappedResult;
  } catch (error) {
    ElMessage.error('搜索失败，请稍后重试');
    searchResults.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 防抖优化后的搜索处理
const debouncedSearch = debounce(async () => {
  await handleSingleSearch();
}, 300);

const handleSearch = () => {
  if (!searchKeyword.value.trim()) {
    ElMessage.warning('请输入搜索内容');
    return;
  }
  debouncedSearch();
};

// 键盘事件处理
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    handleSearch();
  }
};

// 分页处理
const handlePageChange = (page: number) => {
  currentPage.value = page;
  // 注意：新API可能不支持分页，这里保留接口以备将来使用
  handleSingleSearch();
};
</script>

<style scoped>
.card-container {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  padding: 1.5rem;
}
</style>