<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索内容空值验证演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .demo-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .demo-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #ffc107;
            padding-bottom: 5px;
        }
        .warning-card {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .test-scenario {
            background: #e3f2fd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }
        .preview-box {
            border: 2px dashed #ffc107;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            background: #fffbf0;
        }
        .highlight {
            background: #ffeb3b;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }
        .icon {
            font-size: 20px;
            margin-right: 8px;
        }
        .code-block {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 10px 0;
            overflow-x: auto;
        }
        .step-list {
            counter-reset: step-counter;
        }
        .step-item {
            counter-increment: step-counter;
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .step-item::before {
            content: "步骤 " counter(step-counter) ": ";
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <h1>⚠️ 搜索内容空值验证演示</h1>
    
    <div class="demo-section">
        <div class="demo-title">🎯 功能说明</div>
        <p>现在当搜索关键词为空时，系统会显示详细的提示信息：</p>
        <ul>
            <li>🔍 <span class="highlight">前端Alert提示</span>：在页面右上角显示详细的警告信息</li>
            <li>💬 <span class="highlight">ElMessage提示</span>：显示简短的警告消息</li>
            <li>📝 <span class="highlight">使用指导</span>：提供具体的输入格式说明</li>
            <li>⏰ <span class="highlight">自动消失</span>：警告提示5秒后自动消失</li>
        </ul>
    </div>

    <div class="demo-section">
        <div class="demo-title">⚠️ 空值检查场景</div>
        
        <div class="warning-card">
            <span class="icon">⚠️</span><strong>场景1：单项搜索内容为空</strong>
            <div class="preview-box">
                <strong>触发条件：</strong>搜索模式为"单项搜索"，搜索框为空或只有空格<br>
                <strong>提示标题：</strong>搜索内容为空<br>
                <strong>提示类型：</strong>warning (橙色)<br>
                <strong>提示内容：</strong>
                <div class="code-block">
请输入要搜索的关键词。

提示：
• 单项搜索：输入一个关键词
• 批量搜索：每行输入一个关键词
                </div>
            </div>
        </div>
        
        <div class="warning-card">
            <span class="icon">⚠️</span><strong>场景2：批量搜索内容为空</strong>
            <div class="preview-box">
                <strong>触发条件：</strong>搜索模式为"批量搜索"，文本框为空或只有空行<br>
                <strong>提示标题：</strong>批量搜索内容为空<br>
                <strong>提示类型：</strong>warning (橙色)<br>
                <strong>提示内容：</strong>
                <div class="code-block">
请输入要搜索的关键词。

批量搜索格式：
• 每行输入一个关键词
• 支持多个关键词同时搜索
• 空行会被自动忽略
                </div>
            </div>
        </div>
        
        <div class="warning-card">
            <span class="icon">⚠️</span><strong>场景3：通用搜索检查</strong>
            <div class="preview-box">
                <strong>触发条件：</strong>点击"开始搜索"按钮时，搜索内容为空<br>
                <strong>提示标题：</strong>搜索内容为空<br>
                <strong>提示类型：</strong>warning (橙色)<br>
                <strong>提示内容：</strong>
                <div class="code-block">
请输入要搜索的关键词。

搜索模式：
• 单项搜索：输入一个关键词
• 批量搜索：每行输入一个关键词
                </div>
            </div>
        </div>
    </div>

    <div class="demo-section">
        <div class="demo-title">🧪 测试步骤</div>
        
        <div class="step-list">
            <div class="step-item">
                访问 <a href="http://localhost:5174/" target="_blank">http://localhost:5174/</a>
            </div>
            
            <div class="step-item">
                测试单项搜索空值检查
                <ul>
                    <li>选择"单项搜索"模式</li>
                    <li>保持搜索框为空</li>
                    <li>点击"开始搜索"按钮</li>
                    <li>观察右上角的警告提示</li>
                </ul>
            </div>
            
            <div class="step-item">
                测试批量搜索空值检查
                <ul>
                    <li>选择"批量搜索"模式</li>
                    <li>保持文本框为空或只输入空行</li>
                    <li>点击"开始搜索"按钮</li>
                    <li>观察右上角的警告提示</li>
                </ul>
            </div>
            
            <div class="step-item">
                测试只有空格的情况
                <ul>
                    <li>在搜索框中只输入空格</li>
                    <li>点击"开始搜索"按钮</li>
                    <li>验证系统能正确识别并提示</li>
                </ul>
            </div>
            
            <div class="step-item">
                观察提示行为
                <ul>
                    <li>警告提示会在页面右上角显示</li>
                    <li>提示包含详细的使用说明</li>
                    <li>5秒后自动消失</li>
                    <li>可以手动点击关闭</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="demo-section">
        <div class="demo-title">🔧 验证逻辑</div>
        
        <div class="test-scenario">
            <h4>检查条件：</h4>
            <ul>
                <li><code>!searchKeyword.value.trim()</code> - 检查是否为空或只有空格</li>
                <li>批量搜索额外检查过滤后的关键词数组长度</li>
            </ul>
            
            <h4>处理流程：</h4>
            <div class="code-block">
输入检查 → 空值判断 → 显示警告提示 → 阻止搜索执行
    ↓
trim()处理 → 过滤空行 → 验证有效内容 → 继续或阻止
            </div>
            
            <h4>提示特点：</h4>
            <ul>
                <li>🎨 使用统一的Alert组件样式</li>
                <li>📝 提供具体的使用指导</li>
                <li>⏰ 自动消失，不干扰用户</li>
                <li>🚫 阻止无效的搜索请求</li>
            </ul>
        </div>
    </div>

    <div class="demo-section">
        <div class="demo-title">💡 用户体验优化</div>
        
        <div style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
            <h4>优化点：</h4>
            <ul>
                <li><strong>清晰指导：</strong>不同搜索模式提供对应的使用说明</li>
                <li><strong>视觉反馈：</strong>使用橙色警告色，引起注意但不过于严重</li>
                <li><strong>多层提示：</strong>Alert详细信息 + ElMessage简短提示</li>
                <li><strong>智能处理：</strong>自动过滤空格和空行</li>
            </ul>
            
            <h4>防止的问题：</h4>
            <ul>
                <li>❌ 避免发送空的搜索请求</li>
                <li>❌ 防止浪费API调用次数</li>
                <li>❌ 减少用户困惑</li>
                <li>❌ 提升整体用户体验</li>
            </ul>
        </div>
    </div>

    <div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <strong>✅ 功能完成！</strong>
        <p>现在当搜索内容为空时，系统会显示友好的警告提示，指导用户正确输入搜索关键词，提升了用户体验和系统的健壮性！</p>
    </div>
</body>
</html>
