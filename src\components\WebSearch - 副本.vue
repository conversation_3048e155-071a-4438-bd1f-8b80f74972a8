<template>
  <div class="space-y-6" v-loading="loading">
    <div class="card-container">
      <div class="flex items-center space-x-4 mb-6">
        <el-radio-group v-model="searchType" class="flex items-center space-x-4">
          <el-radio :value="0">单项搜索</el-radio>
          <el-radio :value="1">批量搜索</el-radio>
        </el-radio-group>
      </div>
      <div class="relative">
        <el-input v-model="searchKeyword" :type="searchType === 0 ? 'text' : 'textarea'"
          :rows="searchType === 1 ? 4 : 1" :placeholder="searchType === 0 ? '请输入搜索内容' : '请输入多个搜索内容，每行一个'"
          class="!rounded-lg" size="large" @keydown="handleKeyDown">
          <template #prefix>
            <el-icon>
              <Search />
            </el-icon>
          </template>
        </el-input>
      </div>
      <div class="flex items-center space-x-4 mt-4">
        <div class="w-[200px]">
          <el-select v-model="searchFilter" placeholder="时间筛选" size="large">
            <el-option label="不限" value="noLimit" />
            <el-option label="一天内" value="oneDay" />
            <el-option label="一周内" value="oneWeek" />
            <el-option label="一个月内" value="oneMonth" />
            <el-option label="一年内" value="oneYear" />
            <el-option label="指定日期" value="specificDate" />
            <el-option label="日期范围" value="dateRange" />
          </el-select>
        </div>
        <div class="w-56" v-if="searchFilter === 'specificDate'">
          <el-date-picker v-model="specificDate" type="date" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
            placeholder="选择日期" :disabled-date="disabledFutureDate" size="large"/>
        </div>

        <div class="w-72" v-if="searchFilter === 'dateRange'">
          <el-date-picker v-model="dateRange" type="daterange"
            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 300px" :disabled-date="disabledFutureDate" size="large" />
        </div>

        <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="handleSearch" size="large">
          开始搜索
        </el-button>
      </div>
    </div>
    <div class="card-container">
      <div class="flex justify-between items-center mb-4" v-if="searchResults.length">
        <h3 class="text-lg font-medium">搜索结果</h3>
        <span class="text-gray-500">共 {{ (total).toLocaleString() }} 条结果</span>
      </div>
      <div class="space-y-6">
        <el-skeleton v-if="loading" :rows=5 animated />
        <template v-else>
          <el-empty v-if="!searchResults.length" description="暂无数据" />
          <div v-else v-for="(item, index) in searchResults" :key="index"
            class="pb-6 border-b border-gray-100 last:border-none">
            <a :href="item.url" target="_blank" class="text-lg font-medium text-blue-600 hover:text-blue-700 cursor-pointer mb-2 line-clamp-1">
              {{ item.title }}
            </a>
            <div class="text-gray-500 text-sm mb-2 flex items-center gap-x-4">
              <div class="flex items-center gap-x-1 text-gray-500 text-sm">
                <el-icon>
                  <Link />
                </el-icon>
                来源：{{ item.source }} 

              </div>
              <div class="flex items-center gap-x-2 text-gray-500 text-sm">
                <el-icon>
                  <Calendar />
                </el-icon> {{ item.date }}
              </div>
            </div>
            <p class="text-gray-600 line-clamp-3">
              {{ item.content }}
            </p>
          </div>
        </template>
      </div>
      <div class="flex justify-center mt-6">
        <div class=""  v-if="searchResults.length && total > 10">
          <el-pagination v-model:current-page="currentPage" :page-size="10" :total="total" layout="prev, pager, next" @current-change="handlePageChange" />
        </div>
        <el-button v-if="!searchResults.length && currentPage > 1" type="primary" class="ml-4" @click="currentPage = 1; handlePageChange(1)">返回第一页</el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Search, Link, Calendar } from "@element-plus/icons-vue";
import { ref, getCurrentInstance, onMounted, watch } from "vue";
import axios from "axios";
import { ElLoading } from "element-plus";
import { he } from "element-plus/es/locales.mjs";

// 添加防抖函数
const debounce = (fn: Function, delay: number) => {
  let timer: number | undefined = undefined;
  return (...args: any[]) => {
    if (timer) window.clearTimeout(timer);
    timer = window.setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  };
};

interface SearchResult {
  title: string;
  url: string;
  source: string;
  date: string;
  content: string;
}

interface CacheData {
  result: SearchResult[];
  total: number;
  timestamp: number;
}

interface APIWebPage {
  name?: string;
  title?: string;
  url?: string;
  siteName?: string;
  datePublished?: string;
  date?: string;
  publishedDate?: string;
  snippet?: string;
  description?: string;
  content?: string;
}

interface APIWebPages {
  totalEstimatedMatches?: number;
  value?: APIWebPage[];
}

interface APIResponse {
  code: number;
  data: {
    webPages: APIWebPages;
  };
  webPages?: APIWebPages;  // 兼容直接返回在顶层的情况
}

const searchType = ref(0);

watch(searchType, () => {
  currentPage.value = 1;
  searchResults.value = [];
  total.value = 0;
});
const searchKeyword = ref("");
const searchFilter = ref("noLimit");
const currentPage = ref(1);
const loading = ref(false);
const total = ref(0);
const specificDate = ref("");
const formatDatePicker = (date) => {
  if (!date) return '';
  const d = new Date(date);
  return `${d.getFullYear()}-${(d.getMonth() + 1).toString().padStart(2, '0')}-${d.getDate().toString().padStart(2, '0')}`;
};
const dateRange = ref<[Date, Date] | []>([]);
const searchResults = ref<SearchResult[]>([]);
const CACHE_EXPIRY_DAYS = 3;
const CACHE_KEY = 'web_search_cache';


// 格式化日期函数
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
};

// 缓存管理函数
const cacheManager = {
  getCache(key: string): CacheData | null {
    try {
      const cachedDataStr = localStorage.getItem(key);
      if (!cachedDataStr) return null;
      
      const cachedData: CacheData = JSON.parse(cachedDataStr);
      const now = Date.now();
      
      if (now - cachedData.timestamp <= CACHE_EXPIRY_DAYS * 24 * 60 * 60 * 1000) {
        return cachedData;
      }
      
      localStorage.removeItem(key);
      return null;
    } catch {
      return null;
    }
  },
  
  setCache(key: string, data: SearchResult[], totalCount: number) {
    try {
      localStorage.setItem(
        key,
        JSON.stringify({
          result: data,
          total: totalCount,
          timestamp: Date.now()
        })
      );
    } catch (error) {
      console.error('缓存存储失败:', error);
    }
  },
  
  generateKey(query: string, page: number): string {
    return `${CACHE_KEY}${query}_${searchFilter.value}_${
      searchFilter.value === 'dateRange' && dateRange.value.length === 2
        ? `${formatDatePicker(dateRange.value[0])}_${formatDatePicker(dateRange.value[1])}`
        : specificDate.value
    }_${page}`;
  }
};

// 修改单项搜索处理函数
const handleSingleSearch = async () => {
  if (!searchKeyword.value.trim()) {
    // @ts-ignore
    window.ElMessage && window.ElMessage.warning('请输入搜索内容');
    return;
  }

  const cacheKey = cacheManager.generateKey(searchKeyword.value, currentPage.value);
  const cachedData = cacheManager.getCache(cacheKey);
  
  if (cachedData) {
    searchResults.value = cachedData.result;
    total.value = cachedData.total;
    return;
  }

  try {
    loading.value = true;
    const data = await searchAPI(searchKeyword.value, currentPage.value);
    const webPages = data.webPages || data.data?.webPages;
    const result = webPages?.value || [];
    total.value = webPages?.totalEstimatedMatches || 0;
    
    const mappedResult = result.map(item => ({
      title: item.name || item.title || '',
      url: item.url || '',
      source: item.siteName || '',
      date: formatDate(item.datePublished || item.date || item.publishedDate || ''),
      content: item.snippet || item.description || item.content || ''
    }));
    // console.log(data);
    cacheManager.setCache(cacheKey, mappedResult, total.value);
    searchResults.value = mappedResult;
  } catch (error) {
    // @ts-ignore
    window.ElMessage && window.ElMessage.error('搜索失败，请稍后重试');
    searchResults.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 修改批量搜索处理函数
const handleBatchSearch = async () => {
  const queries = searchKeyword.value
    .split('\n')
    .map(q => q.trim())
    .filter(q => q);

  if (queries.length === 0) {
    // @ts-ignore
    window.ElMessage && window.ElMessage.warning('请输入搜索内容');
    return;
  }

  try {
    loading.value = true;
    let allResults: SearchResult[] = [];
    let totalCount = 0;

    for (const query of queries) {
      const cacheKey = cacheManager.generateKey(query, 1);
      const cachedData = cacheManager.getCache(cacheKey);

      if (cachedData) {
        allResults = allResults.concat(cachedData.result);
        totalCount += cachedData.total;
        continue;
      }

      const data = await searchAPI(query, 1);
      const webPages = data.webPages || data.data?.webPages;
      const result = webPages?.value || [];
      
      const mapped = result.map(item => ({
        title: item.name || item.title || '',
        url: item.url || '',
        source: item.siteName || '',
        date: formatDate(item.datePublished || item.date || item.publishedDate || ''),
        content: item.snippet || item.description || item.content || ''
      }));

      cacheManager.setCache(cacheKey, mapped, webPages?.totalEstimatedMatches || 0);
      allResults = allResults.concat(mapped);
      totalCount += webPages?.totalEstimatedMatches || 0;
    }

    searchResults.value = allResults;
    total.value = totalCount;
  } catch (error) {
    // @ts-ignore
    window.ElMessage && window.ElMessage.error('批量搜索失败，请稍后重试');
    searchResults.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 防抖优化后的搜索处理
const debouncedSearch = debounce(async () => {
  if (searchFilter.value === 'specificDate') {
    console.log('指定日期:', specificDate.value);
  }
  if (searchType.value === 0) {
    await handleSingleSearch();
  } else {
    await handleBatchSearch();
  }
}, 300);

const handleSearch = () => {
  if (!searchKeyword.value.trim()) {
    // @ts-ignore
    window.ElMessage && window.ElMessage.warning('请输入搜索内容');
    return;
  }
  debouncedSearch();
};

const handlePageChange = async (page) => {
  currentPage.value = page;
  searchResults.value = []; // 清空之前的结果
  await handleSearch();
  if (!searchResults.value.length && page > 1) {
    // @ts-ignore
    window.ElMessage && window.ElMessage.warning('当前页无数据');
  }
};
// API请求函数
async function searchAPI(query: string, page = 1): Promise<APIResponse> {
  try {
    let freshnessValue = searchFilter.value;
    if (searchFilter.value === 'specificDate' && specificDate.value) {
      freshnessValue = specificDate.value;
    } else if (searchFilter.value === 'dateRange' && dateRange.value && dateRange.value.length === 2) {
      const startDate = formatDatePicker(dateRange.value[0]);
      const endDate = formatDatePicker(dateRange.value[1]);
      freshnessValue = `${startDate}..${endDate}`;
    }
    
    const response = await axios.post<APIResponse>('https://api.bochaai.com/v1/web-search', 
      {
        query,
        freshness: freshnessValue,
        summary: false,
        count: 10,
        page
      },
      {
        headers: {
          'Authorization': 'sk-29cfe2690641438e9ec710f0c7e721eb',
          'Content-Type': 'application/json'
        }
      }
    );
    console.log(response.data);
    return response.data;
  } catch (error) {
    console.error('搜索请求失败:', error);
    throw error;
  }
}

const disabledFutureDate = (time: Date) => {
  return time.getTime() > Date.now();
};


const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && !event.shiftKey && searchType.value === 0) {
    event.preventDefault();
    handleSearch();
  }
};

// 修改日期监听和搜索触发逻辑
watch(searchFilter, (newVal) => {
  // 当切换到非日期选择时，自动触发搜索
  if (newVal !== 'specificDate' && newVal !== 'dateRange') {
    if (searchKeyword.value.trim()) {
      currentPage.value = 1;
      handleSearch();
    }
  }
});

// 监听具体日期变化
watch(specificDate, (newVal) => {
  if (searchFilter.value === 'specificDate' && newVal && searchKeyword.value.trim()) {
    currentPage.value = 1;
    handleSearch();
  }
});

// 监听日期范围变化
watch(dateRange, (newVal) => {
  if (searchFilter.value === 'dateRange' && newVal && newVal.length === 2 && searchKeyword.value.trim()) {
    currentPage.value = 1;
    handleSearch();
  }
});

// {
//     code:200,
//     data:{
//         images:{
//             value:[
//             {
//                 thumbnailUrl:"http://www.ju1.cn/Public/Home/index/images/ai1.jpg",
//                 height:200,
//                 width:200,
//                 hostPageUrl:"https://m.yxwoo.com/ghjt/118429.html",
//                 width:200,
//                 thumbnailUrl:"https://www.yxwoo.com/uploads/images/20231212/20231212093731_45348.png",
//             }
//             ]
//         },
//         queryContext: {originalQuery: '句易网'},
//         webPages: {
//             totalEstimatedMatches: 793886,
//             value: [
//                 {
//                     name: '句易网',
//                     url: 'http://m.ju1.cn/index.php',
//                     datePublished: "2025-01-01T00:00:00+08:00",
//                     snippet: "句易网为您提供2025年最新广告法淘宝抖音违禁词在线过滤工具，欢迎使用，工具适用于各类行业自媒体短视频文案新闻稿检查，词库包含各类禁语极限用语，不断完善中."
//                 } 
//             ]
//         }
//     }
// }

</script>
<style scoped>
  .card-container{
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
  }
</style>