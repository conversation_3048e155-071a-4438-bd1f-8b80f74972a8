<template>
  <div class="login-container">
    <div class="max-w-md w-full space-y-8 p-8 bg-white/90 rounded-lg shadow-xl login-box">
      <div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">登录系统</h2>
      </div>
      <form class="mt-8 space-y-6" @submit.prevent="handleLogin">
        <div class="rounded-md shadow-sm -space-y-px">
          <div>
            <label for="username" class="sr-only">用户名</label>
            <el-input
              v-model="username"
              type="text"
              placeholder="用户名"
              class="mb-4"
              size="large"
              :prefix-icon="User"
            />
          </div>
          <div>
            <label for="password" class="sr-only">密码</label>
            <el-input
              v-model="password"
              type="password"
              placeholder="密码"
              show-password
              size="large"
              :prefix-icon="Lock"
            />
          </div>
        </div>

        <div>
          <el-button
            type="primary"
            class="w-full !rounded-md login-button"
            @click="handleLogin"
            :loading="loading"
            size="large"
          >
            登录
          </el-button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, Lock } from '@element-plus/icons-vue'

const router = useRouter()
const username = ref('')
const password = ref('')
const loading = ref(false)

// 固定的账号密码
const VALID_USERNAME = 'admin'
const VALID_PASSWORD = 'ggseo123'

const handleLogin = async () => {
  if (!username.value || !password.value) {
    ElMessage.warning('请输入用户名和密码')
    return
  }

  loading.value = true
  
  try {
    // 模拟登录请求延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    if (username.value === VALID_USERNAME && password.value === VALID_PASSWORD) {
      // 保存登录状态
      localStorage.setItem('isLoggedIn', 'true')
      ElMessage.success('登录成功')
      // 跳转到搜索页面
      router.push('/')
    } else {
      ElMessage.error('用户名或密码错误')
    }
  } catch (error) {
    ElMessage.error('登录失败，请重试')
  } finally {
    loading.value = false
  }
}
</script>

<style>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(
    180deg,
    #1e88e5 0%,
    #ffffff 50%,
    #1e88e5 100%
  );
  background-size: 100% 200%;
  animation: gradientMove 15s ease infinite;
}

.login-box {
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.login-button {
  background: linear-gradient(45deg, #1e88e5, #64b5f6) !important;
  border: none !important;
  transition: all 0.3s ease !important;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(30, 136, 229, 0.4);
}

@keyframes gradientMove {
  0% {
    background-position: 0% 0%;
  }
  50% {
    background-position: 0% 100%;
  }
  100% {
    background-position: 0% 0%;
  }
}

/* 输入框样式优化 */
.el-input .el-input__wrapper {
  background: rgba(255, 255, 255, 0.9) !important;
  box-shadow: 0 0 0 1px rgba(30, 136, 229, 0.2) !important;
}

.el-input .el-input__wrapper:hover {
  box-shadow: 0 0 0 1px rgba(30, 136, 229, 0.4) !important;
}

.el-input .el-input__wrapper.is-focus {
  box-shadow: 0 0 0 1px #1e88e5 !important;
}
</style> 