<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试指定网站搜索功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-case {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .test-title {
            font-weight: bold;
            color: #333;
        }
        .test-input {
            background-color: #f5f5f5;
            padding: 10px;
            margin: 5px 0;
            border-radius: 3px;
        }
        .test-expected {
            color: #007bff;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <h1>指定网站搜索功能测试用例</h1>
    
    <div class="test-case">
        <div class="test-title">测试用例 1: 单个域名</div>
        <div class="test-input">输入: qq.com</div>
        <div class="test-expected">期望: 请求参数中包含 include: "qq.com"</div>
    </div>
    
    <div class="test-case">
        <div class="test-title">测试用例 2: 多个域名用|分隔</div>
        <div class="test-input">输入: qq.com|163.com|sina.com</div>
        <div class="test-expected">期望: 请求参数中包含 include: "qq.com|163.com|sina.com"</div>
    </div>
    
    <div class="test-case">
        <div class="test-title">测试用例 3: 多个域名用,分隔</div>
        <div class="test-input">输入: qq.com,163.com,sina.com</div>
        <div class="test-expected">期望: 请求参数中包含 include: "qq.com|163.com|sina.com"</div>
    </div>
    
    <div class="test-case">
        <div class="test-title">测试用例 4: 子域名</div>
        <div class="test-input">输入: m.163.com|news.qq.com</div>
        <div class="test-expected">期望: 请求参数中包含 include: "m.163.com|news.qq.com"</div>
    </div>
    
    <div class="test-case">
        <div class="test-title">测试用例 5: 超过20个域名</div>
        <div class="test-input">输入: 21个域名</div>
        <div class="test-expected">期望: 显示警告信息，只保留前20个域名</div>
    </div>
    
    <div class="test-case">
        <div class="test-title">测试用例 6: 无效域名格式</div>
        <div class="test-input">输入: .invalid.com|valid.com|another.invalid.</div>
        <div class="test-expected">期望: 显示警告信息，过滤无效域名，只保留 valid.com</div>
    </div>
    
    <h2>测试步骤</h2>
    <ol>
        <li>打开 http://localhost:5174/</li>
        <li><strong>打开浏览器开发者工具（F12），切换到Console标签</strong></li>
        <li>开启"指定网站搜索"开关（会在控制台看到开关状态变化日志）</li>
        <li>在域名输入框中输入测试用例（会在控制台看到域名处理日志）</li>
        <li>输入搜索关键词</li>
        <li>点击"开始搜索"</li>
        <li><strong>在控制台查看详细的搜索日志输出</strong></li>
        <li>在Network标签中查看请求参数验证include参数</li>
    </ol>

    <h2>控制台日志说明</h2>
    <div class="test-case">
        <div class="test-title">🔄 开关状态变化</div>
        <div class="test-input">当开启/关闭指定网站搜索时，会显示状态变化信息</div>
    </div>

    <div class="test-case">
        <div class="test-title">📝 域名输入处理</div>
        <div class="test-input">输入域名时会显示原始输入和处理后的域名格式</div>
    </div>

    <div class="test-case">
        <div class="test-title">🎯 搜索请求参数</div>
        <div class="test-input">显示完整的API请求参数，包括include字段</div>
    </div>

    <div class="test-case">
        <div class="test-title">📊 搜索结果统计</div>
        <div class="test-input">显示指定网站搜索的结果数量和详细数据</div>
    </div>

    <div class="test-case">
        <div class="test-title">🔄 批量搜索日志</div>
        <div class="test-input">批量搜索时会显示每个关键词的搜索结果和汇总信息</div>
    </div>
    
    <h2>验证要点</h2>
    <ul>
        <li>开关关闭时，不应该有include参数</li>
        <li>开关开启但域名为空时，不应该有include参数</li>
        <li>域名格式验证是否正确</li>
        <li>域名数量限制是否生效</li>
        <li>分隔符处理是否正确（|和,都支持）</li>
    </ul>
</body>
</html>
