# 指定网站搜索控制台输出演示

## 功能说明
现在当您使用指定网站搜索功能时，控制台会详细打印出所有相关的数据和状态变化。

## 控制台输出内容

### 1. 开关状态变化
当您开启或关闭"指定网站搜索"开关时：
```
🔄 指定网站搜索开关状态变化: 开启
```
或
```
🔄 指定网站搜索开关状态变化: 关闭
🧹 已清空域名输入
🔄 关闭指定网站搜索，重新执行普通搜索
```

### 2. 域名输入处理
当您在域名输入框中输入内容时：
```
📝 域名输入变化:
原始输入: qq.com|163.com,sina.com
处理后域名: qq.com|163.com|sina.com
```

### 3. 搜索请求参数
每次搜索时会显示完整的API请求参数：
```
=== 搜索请求参数 ===
{
  query: "人工智能",
  freshness: "noLimit",
  summary: false,
  count: 50,
  page: 1,
  include: "qq.com|163.com|sina.com"
}
🎯 指定网站搜索已启用
📍 指定域名: qq.com|163.com|sina.com
```

### 4. 搜索响应数据
API返回的完整响应数据：
```
=== 搜索响应数据 ===
{
  code: 200,
  data: {
    webPages: {
      totalEstimatedMatches: 1234,
      value: [
        {
          name: "人工智能最新发展...",
          url: "https://tech.qq.com/...",
          siteName: "腾讯科技",
          datePublished: "2025-01-07T10:00:00+08:00",
          snippet: "人工智能技术在各个领域..."
        }
        // ... 更多结果
      ]
    }
  }
}
```

### 5. 单项搜索结果统计
```
🔍 指定网站搜索结果统计:
📊 总结果数: 1234
📄 当前页结果数: 10
📋 结果详情: [详细的结果数组]
```

### 6. 批量搜索日志
当使用批量搜索时：
```
🔄 批量指定网站搜索开始
📝 搜索关键词列表: [人工智能, 机器学习, 深度学习]
🌐 指定域名: qq.com|163.com

🔍 关键词 "人工智能" 的搜索结果: {
  totalMatches: 500,
  currentPageResults: 10,
  results: [...]
}

🔍 关键词 "机器学习" 的搜索结果: {
  totalMatches: 300,
  currentPageResults: 8,
  results: [...]
}

📊 批量指定网站搜索汇总:
🎯 总结果数: 800
📄 合并后结果数: 18
```

## 测试步骤

1. 打开 http://localhost:5174/
2. **按F12打开开发者工具，切换到Console标签**
3. 开启"指定网站搜索"开关
4. 输入域名，如：`qq.com|163.com`
5. 输入搜索关键词，如：`人工智能`
6. 点击"开始搜索"
7. **观察控制台的详细输出**

## 验证要点

- ✅ 开关状态变化有日志输出
- ✅ 域名输入处理有详细信息
- ✅ API请求参数包含include字段
- ✅ 响应数据完整显示
- ✅ 搜索结果统计清晰
- ✅ 批量搜索每步都有记录

现在您可以清楚地看到指定网站搜索功能的完整工作流程和返回的数据！
