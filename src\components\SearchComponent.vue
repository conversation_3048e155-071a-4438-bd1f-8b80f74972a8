<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 顶部导航 -->
    <header class="fixed left-0 top-0 w-full bg-white shadow-sm z-50">
      <nav class="max-w-7xl mx-auto px-4 h-16 flex items-center justify-between">
        <div class="items-center space-x-2 hidden md:flex">
          
            <el-icon class="text-blue-600 text-xl">
            <Monitor />
          </el-icon>
          <a href="/"> <span class="text-xl font-medium">暴走蜘蛛GEO系统</span>
          </a>
        </div>
        
        <div class="flex items-center md:space-x-4">
          <div class="">
          <a href="http://114.66.55.234:28888/mycloud/admin/" target="_blank"> 
            <el-button type="primary" class="flex items-center space-x-2"> 
              <el-icon><TopRight /></el-icon>
              <span>GEO智能监控平台</span>
            </el-button>
          </a>
        </div>
          <div v-for="(item, index) in menus" :key="index" @click="activeMenu = index" :class="[
            'flex items-center space-x-2 py-2 px-4 !rounded-button whitespace-nowrap cursor-pointer',
            activeMenu === index
              ? 'text-blue-600'
              : 'text-gray-600 hover:text-blue-600',
          ]">
            <el-icon>
              <component :is="item.icon" />
            </el-icon>
            <span>{{ item.name }}</span>
          </div>
        </div>
        <div class="header-actions hidden md:block">
          <el-button type="danger" @click="handleLogout" class="logout-button">
            <el-icon><SwitchButton /></el-icon>
            退出登录
          </el-button>
        </div>
      </nav>
    </header>
    <!-- 主内容区 -->
    <main class="pt-20 pb-8 max-w-7xl mx-auto px-4">
      <!-- 网页搜索 -->
      <WebSearch v-if="activeMenu === 0" />
      <!-- AI搜索 -->
      <AISearch v-if="activeMenu === 1" />
      <!-- 语义排序 -->
      <SemanticSort v-if="activeMenu === 2" />
      <!-- 提示词策略师 -->
      <WordPage v-if="activeMenu === 3" />
      <!-- 英文组件 -->
      <EnComponent v-if="activeMenu === 4" />
    </main>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { SwitchButton } from '@element-plus/icons-vue'
import {
  Monitor,
  Search,
  Sort,
  Orange,
  Reading,
  Open
} from "@element-plus/icons-vue";
import WebSearch from "./WebSearch.vue";
import AISearch from "./AISearch.vue";
import SemanticSort from "./SemanticSort.vue";
import WordPage from "./Word.vue";
import EnComponent from "./EnComponent.vue";

//import * as echarts from 'echarts';
const menus = [
  { name: "模拟抓取", icon: Search },
  { name: "AI 搜索", icon: Orange },
  { name: "语义排序", icon: Sort },
  { name: "提示词策略师", icon: Reading },
  { name: "英文", icon: Open },
];
const activeMenu = ref(0);

const router = useRouter()

onMounted(() => {
  // 确保初始状态下显示第一个菜单的内容
  activeMenu.value = 0;
});

const disabledFutureDate = (time: Date) => {
  return time.getTime() > Date.now();
};

// 退出登录处理
const handleLogout = () => {
  // 清除登录状态
  localStorage.removeItem('isLoggedIn')
  ElMessage.success('已退出登录')
  // 跳转到登录页
  router.push('/login')
}

</script>
<style scoped>
.container {
  position: relative;
  padding: 20px;
}

.header-actions {
  /* position: absolute; */
  top: 20px;
  right: 20px;
  z-index: 10;
}

.logout-button {
  display: flex;
  align-items: center;
  gap: 5px;
}

.card-container {
  background-color: white;
  border-radius: 0.5rem;
  padding: 1.5rem;
  /* box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1); */
}

.el-input :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #e5e7eb;
}

.el-input :deep(.el-input__wrapper):hover {
  box-shadow: 0 0 0 1px #1890ff;
}

.el-input :deep(.el-input__inner) {
  font-size: 14px;
}

.el-textarea :deep(.el-textarea__inner) {
  font-size: 14px;
}
</style>
