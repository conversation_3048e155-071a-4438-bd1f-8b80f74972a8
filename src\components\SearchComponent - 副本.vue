<template>
  <div class="search-container" v-loading.fullscreen.lock="loading" element-loading-text="加载中...">
    <el-backtop :right="40" :bottom="40" :visibility-height="200" />

    <div class="search-mode-switch">
      <el-button-group>
        <el-button :type="activeTab === 'single' ? 'primary' : 'default'" @click="activeTab = 'single'">单个搜索</el-button>
        <el-button :type="activeTab === 'batch' ? 'primary' : 'default'" @click="activeTab = 'batch'">批量搜索</el-button>
      </el-button-group>
    </div>

    <div v-if="activeTab === 'single'" class="search-input-container">
      <el-input
        style="width: 240px"
        v-model="singleSearchQuery"
        placeholder="请输入搜索内容"
        :disabled="loading"
        @keyup.enter="handleSingleSearch"
      >
        <template #append>
          <el-button :loading="loading" @click="handleSingleSearch">搜索</el-button>
        </template>
      </el-input>
    </div>

    <div v-else class="search-input-container">
      <el-input
        v-model="batchSearchQuery"
        type="textarea"
        :rows="4"
        placeholder="请输入多个搜索内容，每行一个"
        :disabled="loading"
      />
      <el-button
        type="primary"
        :loading="loading"
        @click="handleBatchSearch"
        style="margin-top: 16px"
      >
        批量搜索
      </el-button>
    </div>

    <!-- 搜索结果展示 -->
    <div class="search-results" v-if="searchResults.length > 0">
        搜索结果: <el-text type="primary" tag="b">{{tiao}}</el-text> 条相关内容
      <div class="result-list">
        <div v-for="(item, itemIndex) in currentPageData" :key="itemIndex">
          <template v-if="item.result?.data?.webPages?.value?.length">
            <div v-for="(page, index) in item.result.data.webPages.value" :key="index" class="result-item">
              <h3 class="result-title">
                <el-link :href="page.url" target="_blank">
                  <el-text class="mx-1" size="large" tag="b" type="primary">{{ page.name || item.query }}</el-text>
                </el-link>
              </h3>
              <div class="result-meta">
                <span class="result-source" v-if="page.siteName">来源：{{ page.siteName }}</span>
                <span class="result-date" v-if="page.dateLastCrawled">{{ new Date(page.dateLastCrawled).toLocaleDateString() }}</span>
              </div>
              <p class="result-summary" v-if="page.snippet">{{ page.snippet }}</p>
            </div>
          </template>
          <div v-else class="no-results">
            <p>未找到相关结果</p>
          </div>
        </div>
      </div>
      
      <div class="pagination-container" v-if="totalPages > 1">
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="totalPages * pageSize"
          @current-change="handlePageChange"
          layout="prev, pager, next"
        />
      </div>
    </div>

    <!-- 无数据提示 -->
    <el-empty v-else-if="!loading" description="暂无搜索结果" />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import axios from 'axios'

// 状态变量
const activeTab = ref('single')
const singleSearchQuery = ref('')
const batchSearchQuery = ref('')
const loading = ref(false)
const searchResults = ref([])
const tiao = ref('')
// 分页相关
const currentPage = ref(1)
const pageSize = 10

// 计算属性
const totalPages = computed(() => {
  if (searchResults.value.length === 0) return 0
  if (activeTab.value === 'single') {
    return Math.ceil(searchResults.value[0].result.data.webPages.totalEstimatedMatches / pageSize)
  } else {
    return Math.ceil(searchResults.value.reduce((total, item) => {
      return total + (item.result?.data?.webPages?.totalEstimatedMatches || 0)
    }, 0) / pageSize)
  }
})

const currentPageData = computed(() => {
  if (searchResults.value.length === 0) return []
  if (activeTab.value === 'single') {
    return searchResults.value.slice(0, 1)
  } else {
    return searchResults.value
  }
})

// 防抖函数
function debounce(fn, delay) {
  let timer = null
  return function (...args) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => fn.apply(this, args), delay)
  }
}

// API请求函数
async function searchAPI(query, page = 1) {
  try {
    const response = await axios.post('https://api.bochaai.com/v1/web-search', 
      {
        query,
        freshness: 'noLimit',
        summary: false,
        count: 10,
        page
      },
      {
        headers: {
          'Authorization': 'sk-29cfe2690641438e9ec710f0c7e721eb',
          'Content-Type': 'application/json'
        }
      }
    )
    return response.data
  } catch (error) {
    console.error('搜索请求失败:', error)
    throw error
  }
}

// 单个搜索处理
const handleSingleSearch = debounce(async () => {
  if (!singleSearchQuery.value.trim()) {
    ElMessage.warning('请输入搜索内容')
    return
  }

  try {
    loading.value = true
    const result = await searchAPI(singleSearchQuery.value)
    searchResults.value = [{ query: singleSearchQuery.value, result }]
    currentPage.value = 1
    tiao.value = searchResults.value[0].result.data.webPages.totalEstimatedMatches
    console.log('搜索结果:', searchResults.value[0].result.data.webPages.totalEstimatedMatches)

  } catch (error) {
    ElMessage.error('搜索失败，请稍后重试')
  } finally {
    loading.value = false
  }
}, 300)

// 批量搜索处理
const handleBatchSearch = async () => {
  const queries = batchSearchQuery.value
    .split('\n')
    .map(q => q.trim())
    .filter(q => q)

  if (queries.length === 0) {
    ElMessage.warning('请输入搜索内容')
    return
  }

  try {
    loading.value = true
    const results = await Promise.all(queries.map(async query => {
      const result = await searchAPI(query)
      return { query, result }
    }))
    searchResults.value = results
    currentPage.value = 1
  } catch (error) {
    ElMessage.error('批量搜索失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 页码改变处理
const handlePageChange = async (page) => {
  currentPage.value = page
  loading.value = true
  try {
    if (activeTab.value === 'single') {
      const result = await searchAPI(singleSearchQuery.value, page)
      searchResults.value = [{ query: singleSearchQuery.value, result }]
      tiao.value = result.data.webPages.totalEstimatedMatches
    } else {
      const queries = batchSearchQuery.value
        .split('\n')
        .map(q => q.trim())
        .filter(q => q)
      const results = await Promise.all(queries.map(async query => {
        const result = await searchAPI(query, page)
        return { query, result }
      }))
      searchResults.value = results
    }
  } catch (error) {
    ElMessage.error('获取数据失败，请稍后重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
:deep(.el-backtop) {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

:deep(.el-backtop:hover) {
  transform: scale(1.1);
  background-color: #409EFF;
  color: white;
}

.search-container {
  min-width: 600px;
  margin: 0 auto;
  padding: 20px;
  text-align: left;
}

.search-mode-switch {
  margin-bottom: 20px;
  text-align: center;
}

.search-input-container {
  margin: 20px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.result-list {
  margin-top: 20px;
}

.result-item {
  padding: 16px;
  transition: background-color 0.3s;
  background-color: #f9f9f9;
  margin-bottom: 10px;
}

.result-title {
  margin: 0 0 8px;
  font-size: 24px;
  font-weight: bold;
  cursor: pointer;
  color: #409EFF;
}

.result-title:hover {
  color: #409EFF;
}

.result-summary {
  margin: 0 0 8px;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.result-meta {
  font-size: 12px;
  color: #999;
  margin-bottom: 10px;
}

.result-source {
  margin-right: 16px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>