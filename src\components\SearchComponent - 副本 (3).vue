<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 顶部导航 -->
    <header class="fixed left-0 top-0 w-full bg-white shadow-sm z-50">
      <nav class="max-w-7xl mx-auto px-4 h-16 flex items-center justify-between">
        <div class="flex items-center space-x-2">
          <el-icon class="text-blue-600 text-xl">
            <Monitor />
          </el-icon>
          <span class="text-xl font-medium">智能工具平台</span>
        </div>
        <div class="flex items-center space-x-8">
          <div v-for="(item, index) in menus" :key="index" @click="activeMenu = index" :class="[
            'flex items-center space-x-2 py-2 px-4 !rounded-button whitespace-nowrap cursor-pointer',
            activeMenu === index
              ? 'text-blue-600'
              : 'text-gray-600 hover:text-blue-600',
          ]">
            <el-icon>
              <component :is="item.icon" />
            </el-icon>
            <span>{{ item.name }}</span>
          </div>
        </div>
      </nav>
    </header>
    <!-- 主内容区 -->
    <main class="pt-20 pb-8 max-w-7xl mx-auto px-4">
      <!-- 网页搜s索 -->
      <div v-if="activeMenu === 0" class="space-y-6">
        <div class="card-container">
          <div class="flex items-center space-x-4 mb-6">
            <el-radio-group v-model="searchType" class="flex items-center space-x-4">
              <el-radio :value="0">单项搜索</el-radio>
              <el-radio :value="1">批量搜索</el-radio>
            </el-radio-group>
          </div>
          <div class="relative">
            <el-input v-model="searchKeyword" :type="searchType === 0 ? 'text' : 'textarea'"
              :rows="searchType === 1 ? 4 : 1" :placeholder="searchType === 0 ? '请输入搜索内容' : '请输入多个搜索内容，每行一个'"
              class="!rounded-lg" size="large">
              <template #prefix>
                <el-icon>
                  <Search />
                </el-icon>
              </template>
            </el-input>
          </div>
          <div class="flex items-center space-x-4 mt-4">
            <div class="w-[200px]">
              <el-select v-model="searchFilter" placeholder="时间筛选">
                <el-option label="不限" value="noLimit" />
                <el-option label="一天内" value="oneDay" />
                <el-option label="一周内" value="oneWeek" />
                <el-option label="一个月内" value="oneMonth" />
                <el-option label="一年内" value="oneYear" />
                <el-option label="指定日期" value="specificDate" />
                <el-option label="日期范围" value="dateRange" />
              </el-select>
            </div>
            <div class="w-56" v-if="searchFilter === 'specificDate'">
              <el-date-picker  v-model="specificDate" type="date"
                placeholder="选择日期" :disabled-date="disabledFutureDate" />
            </div>

            <div class="w-72" v-if="searchFilter === 'dateRange'">
              <el-date-picker v-model="dateRange" type="daterange"
                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 300px" :disabled-date="disabledFutureDate" />
            </div>

            <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="handleSearch">
              开始搜索
            </el-button>
          </div>
        </div>
        <div class="card-container">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium">搜索结果</h3>
            <span class="text-gray-500">共 {{ (8901).toLocaleString() }} 条结果</span>
          </div>
          <div class="space-y-6">
            <div v-for="(item, index) in searchResults" :key="index"
              class="pb-6 border-b border-gray-100 last:border-none">
              <h4 class="text-lg font-medium text-blue-600 hover:text-blue-700 cursor-pointer mb-2 line-clamp-1">
                {{ item.title }}
              </h4>
              <div class="text-gray-500 text-sm mb-2">
                来源：{{ item.source }} · {{ item.date }}
              </div>
              <p class="text-gray-600 line-clamp-3">
                {{ item.content }}
              </p>
            </div>
          </div>
          <div class="flex justify-center mt-6">
            <el-pagination v-model:current-page="currentPage" :page-size="10" :total="89" layout="prev, pager, next" />
          </div>
        </div>
      </div>
      <!-- AI搜索 -->
      <div v-if="activeMenu === 1" class="space-y-6">
        <div class="card-container">
          <h3 class="text-lg font-medium mb-4">AI 搜索</h3>
          <div class="flex space-x-4">
            <el-input v-model="aiQuery" placeholder="请输入您的问题" class="flex-1">
              <template #prefix>
                <el-icon>
                  <Search />
                </el-icon>
              </template>
            </el-input>
            <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="handleAiSearch">
              开始搜索
            </el-button>
          </div>
        </div>
        <div class="card-container">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium">AI 分析结果</h3>
            <span class="text-gray-500">共 {{ (3504).toLocaleString() }} 条结果</span>
          </div>
          <div class="space-y-6">
            <div v-for="(item, index) in aiSearchResults" :key="index"
              class="pb-6 border-b border-gray-100 last:border-none">
              <h4 class="text-lg font-medium text-blue-600 hover:text-blue-700 cursor-pointer mb-2">
                {{ item.title }}
              </h4>
              <div class="text-gray-500 text-sm mb-2">
                <el-icon>
                  <Calendar />
                </el-icon>
                <span class="ml-1">{{ item.date }}</span>
                <el-tag v-if="item.type === 'encyclopedia'" size="small" class="ml-2" type="info">
                  <el-icon>
                    <Collection />
                  </el-icon>
                  <span class="ml-1">百科</span>
                </el-tag>
                <el-tag v-if="item.type === 'medical'" size="small" class="ml-2" type="danger">
                  <el-icon>
                    <FirstAidKit />
                  </el-icon>
                  <span class="ml-1">医疗</span>
                </el-tag>
              </div>
              <p class="text-gray-600 line-clamp-3">
                {{ item.content }}
              </p>
              <div class="mt-4 grid grid-cols-2 gap-4" v-if="item.images">
                <div v-for="(img, imgIndex) in item.images" :key="imgIndex" class="overflow-hidden rounded-lg">
                  <img :src="img" class="w-full h-32 object-cover" />
                </div>
              </div>
              <div class="mt-2 flex items-center space-x-2 text-sm text-blue-600">
                <el-icon>
                  <Document />
                </el-icon>
                <span>查看完整内容</span>
              </div>
            </div>
          </div>
          <div class="flex justify-center mt-6">
            <el-pagination v-model:current-page="aiCurrentPage" :page-size="10" :total="56"
              layout="prev, pager, next" />
          </div>
        </div>
      </div>
      <!-- 语义排序 -->
      <div v-if="activeMenu === 2" class="space-y-6">
        <div class="card-container">
          <h3 class="text-lg font-medium mb-4">文章输入</h3>
          <div class="mb-6">
            <h4 class="font-medium mb-2">关键词输入</h4>
            <div class="w-1/2">
              <el-input v-model="keywordsInput" placeholder="请输入关键词" class="w-full">
                <template #prefix>
                  <el-icon>
                    <Search />
                  </el-icon>
                </template>
              </el-input>
            </div>
          </div>
          <div class="space-y-4">
            <div v-for="(article, index) in articles" :key="index" class="space-y-2">
              <div class="flex items-center justify-between">
                <h4 class="font-medium">文章 {{ index + 1 }}</h4>
                <el-button v-if="index > 0" type="danger" class="!rounded-button whitespace-nowrap"
                  @click="removeArticle(index)">
                  删除
                </el-button>
              </div>
              <el-input v-model="articles[index]" type="textarea" rows="4" :placeholder="`请输入文章 ${index + 1} 内容`" />
            </div>
          </div>
          <div class="flex justify-between mt-4">
            <el-button v-if="articles.length < 3" type="primary" class="!rounded-button whitespace-nowrap"
              @click="addArticle">
              添加文章
            </el-button>
            <el-button type="success" class="!rounded-button whitespace-nowrap" @click="handleAnalyze">
              开始分析
            </el-button>
          </div>
        </div>
        <div class="card-container">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-medium">评分结果</h3>
            <div class="text-sm text-gray-500">总分：100分</div>
          </div>
          <div class="grid grid-cols-3 gap-6">
            <div v-for="(score, index) in articleScores" :key="index" class="bg-gray-50 rounded-lg p-4 text-center">
              <div class="text-lg font-medium mb-2">文章 {{ index + 1 }}</div>
              <div class="text-3xl font-bold text-blue-600">{{ score }}分</div>
              <div class="mt-2 text-sm text-gray-500">相关度评分</div>
            </div>
          </div>
          <div ref="chartRef" class="w-full h-80 mt-6"></div>
        </div>
      </div>
    </main>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from "vue";
const keywordsInput = ref("");
const specificDate = ref("");
const dateRange = ref([]);
import {
  Monitor,
  Search,
  Document,
  Calendar,
  Collection,
  FirstAidKit,
  CircleCheckFilled,
  WarningFilled,
  CircleCloseFilled,
  Plus,
  Sort,
  Orange
} from "@element-plus/icons-vue";
//import * as echarts from 'echarts';
const menus = [
  { name: "网页搜索", icon: Search },
  { name: "AI 搜索", icon: Orange },
  { name: "语义排序", icon: Sort },
];
const activeMenu = ref(0);

onMounted(() => {
  // 确保初始状态下显示第一个菜单的内容
  activeMenu.value = 0;
});
const searchType = ref(0);
const searchKeyword = ref("");
const searchFilter = ref("noLimit");
const currentPage = ref(1);
const aiQuery = ref("");
const aiCurrentPage = ref(1);
const articles = ref([""]);
const chartRef = ref<HTMLElement>();
const articleScores = ref([92, 85, 78]);
const aiSearchResults = ref([
  {
    title: "GPT-4 在自然语言理解领域的突破性进展",
    date: "2024-01-15",
    type: "encyclopedia",
    content:
      "GPT-4在语言理解和生成方面展现出了前所未有的能力，尤其在复杂上下文理解、多语言翻译和创意写作等任务上表现卓越。本研究深入分析了GPT-4的架构创新和性能提升，为下一代大型语言模型的发展提供了重要参考。",
  },
  {
    title: "深度学习在金融风控中的应用实践",
    date: "2024-01-14",
    type: "medical",
    content:
      "本文详细探讨了深度学习技术如何革新传统金融风控体系。通过构建端到端的神经网络模型，系统能够从海量交易数据中自动学习特征，实现对欺诈交易的实时识别，显著提升了风控效率和准确率。",
  },
  {
    title: "计算机视觉在自动驾驶中的最新突破",
    date: "2024-01-13",
    content:
      "研究团队提出了一种新型的多模态融合算法，能够同时处理视觉、激光雷达和毫米波雷达数据。实验表明，该方法在复杂天气条件下的障碍物检测准确率提升了15%，为自动驾驶技术的进步做出了重要贡献。",
  },
]);
const searchResults = ref([
  {
    title: "深度学习技术在自然语言处理中的应用及最新进展",
    source: "人工智能研究院",
    date: "2024-01-15",
    content:
      "近年来，深度学习技术在自然语言处理领域取得了突破性进展。本文详细介绍了深度学习在机器翻译、文本分类、情感分析等任务中的创新应用，并探讨了未来发展趋势。研究表明，基于Transformer架构的预训练模型显著提升了NLP任务的性能表现。",
  },
  {
    title: "2024年人工智能技术发展趋势分析报告",
    source: "科技创新中心",
    date: "2024-01-14",
    content:
      "本报告全面分析了2024年人工智能领域的关键发展趋势，包括大规模语言模型的演进、多模态AI系统的突破、AI芯片技术创新重点关注方向。报告指出，AI技术正在加速向更多垂直领域渗透，推动产业智能化升级。",
  },
  {
    title: "机器学习算法在推荐系统中的优化与实践",
    source: "数据科学实验室",
    date: "2024-01-13",
    content:
      "本研究着重探讨了机器学习算法在大规模推荐系统中的应用优化策略。通过改进深度学习模型结构、引入注意力机制、优化损失函数等方法，显著提升了推荐系统的性能指标。实验结果表明，优化后的算法在点击率和用户满意度方面都取得了明显提升。",
  },
]);
const aiImage = ref(
  "https://ai-public.mastergo.com/ai/img_res/61c3ff225b7fc0d01c6e2bd0088ae352.jpg"
);
const handleSearch = () => {
  // 搜索逻辑
};
const handleAiSearch = () => {
  // AI搜索处理逻辑
};

const addArticle = () => {
  if (articles.value.length < 3) {
    articles.value.push("");
  }
};
const removeArticle = (index: number) => {
  articles.value.splice(index, 1);
};
const handleAnalyze = () => {
  articleScores.value = [92, 85, 78];
};

const disabledFutureDate = (time: Date) => {
  return time.getTime() > Date.now();
};

</script>
<style scoped>
.card-container {
  background-color: white;
  border-radius: 0.5rem;
  padding: 1.5rem;
  /* box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1); */
}

.el-input :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #e5e7eb;
}

.el-input :deep(.el-input__wrapper):hover {
  box-shadow: 0 0 0 1px #1890ff;
}

.el-input :deep(.el-input__inner) {
  font-size: 14px;
}

.el-textarea :deep(.el-textarea__inner) {
  font-size: 14px;
}
</style>

