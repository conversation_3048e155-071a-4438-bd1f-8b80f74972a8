<template>
  <div class="space-y-6">
    <div class="bg-white p-6 rounded-lg">
      <div class="mb-6">
        
        <div class="mt-1 text-sm text-gray-500 flex items-center mb-4">
              <h3 class="text-lg font-medium mr-2">关键词输入</h3>
              <el-tooltip placement="bottom">
                <template #content> 
                  0.75~1:高度相关并完全回答了问题。<br />
                  0.5~0.75:问题是相关的，但缺乏细节。<br />
                  0.2~0.5:有一定的相关性，它回答了部分问题。<br />
                  0.1~0.2:仅回答了一小部分。<br />
                  0~0.1:无关紧要。
                </template>
                <el-button>
                  <el-icon><QuestionFilled /></el-icon><span class="ml-1">评分说明</span>
                </el-button>
              </el-tooltip>
          </div>
        <div class="w-full">
          <el-input v-model="keywordsInput" size="large" placeholder="请输入关键词" class="w-full">
            <template #prefix>
              <el-icon>
                <Search />
              </el-icon>
            </template>
          </el-input>
        </div>
      </div>
      <div class="space-y-4">
        <div v-for="(article, index) in articles" :key="index" class="space-y-2">
          <div class="flex items-center justify-between">
            <h4 class="font-medium">文章 {{ index + 1 }}</h4>
            <el-button v-if="index > 0" type="danger" class="!rounded-button whitespace-nowrap"
              @click="removeArticle(index)">
              删除
            </el-button>
          </div>
          <el-input v-model="articles[index]" type="textarea" :rows="4" :placeholder="`请输入文章内容`" />
          <div v-if="articleScores[index] !== undefined" class="mt-2 p-3 bg-gray-50 rounded-lg">
            <div class="flex items-center justify-between">
              <span class="text-gray-600">相关度评分：</span>
              <span class="text-xl font-bold text-blue-600">{{ (articleScores[index] / 100).toFixed(4) }}</span>
            </div>
            
          </div>
        </div>
      </div>
      <div class="flex justify-between mt-4">
        <el-button v-if="articles.length < 10" type="primary" class="!rounded-button whitespace-nowrap"
          @click="addArticle">
          添加文章
        </el-button>
        <el-button type="success" class="!rounded-button whitespace-nowrap" @click="handleAnalyze">
          开始分析
        </el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { Search, QuestionFilled } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { ElLoading } from 'element-plus';
import axios from 'axios';

// 更新接口响应类型
interface APIResponse {
  code: number;
  log_id: string;
  msg: string | null;
  data: {
    model: string;
    results: Array<{
      relevance_score: number;
      index: number;
    }>;
  };
}

const keywordsInput = ref("");
const articles = ref([""]);
const chartRef = ref<HTMLElement>();
const articleScores = ref<number[]>([]);
const responseData = ref([""]);

const addArticle = () => {
  if (articles.value.length < 10) {
    articles.value.push("");
  }
};

const removeArticle = (index: number) => {
  articles.value.splice(index, 1);
};

const handleAnalyze = async () => {
  // 验证关键词输入
  if (!keywordsInput.value.trim()) {
    ElMessage.warning('请输入关键词');
    return;
  }

  // 验证文章内容
  const hasEmptyArticle = articles.value.some(article => !article.trim());
  if (hasEmptyArticle) {
    ElMessage.warning('请确保所有文章内容都已填写');
    return;
  }

  const loading = ElLoading.service({
    lock: true,
    text: '正在分析文章相关度...',
    background: 'rgba(255, 255, 255, 0.7)',
  });

  try {
    const requestData = {
      model: "gte-rerank",
      query: keywordsInput.value.trim(),
      documents: articles.value.map(article => article.trim()),
      return_documents: false,
      top_n: 10
    };

    // console.log('发送的请求数据：', requestData);

    const response = await axios.post<APIResponse>(
      'https://api.bochaai.com/v1/rerank', 
      requestData,
      {
        headers: {
          'Authorization': 'sk-29cfe2690641438e9ec710f0c7e721eb',
          'Content-Type': 'application/json'
        }
      }
    );

    console.log('完整的 API 响应：', JSON.stringify(response.data, null, 2));
    // console.log('results 字段：', response.data?.data?.results);
    
    // 处理响应数据，获取 relevance_score
    if (response.data?.data?.results && response.data.data.results.length > 0) {
      // 创建一个与文章数量相同的数组，初始值设为0
      const scores = new Array(articles.value.length).fill(0);
      
      // 根据 index 将分数放入对应位置
      response.data.data.results.forEach(item => {
        if (item.index >= 0 && item.index < scores.length) {
          scores[item.index] = Number((item.relevance_score * 100).toFixed(2));
        }
      });
      
      articleScores.value = scores;
      console.log('计算后的相关度分数：', scores);
    } else {
      // console.error('API 响应数据异常：', response.data);
      ElMessage.warning('未获取到评分结果，请检查输入内容是否合适');
    }
  } catch (error: any) {
    console.error('请求失败：', error);
    
    // 详细的错误信息处理
    if (error.response) {
      // 服务器返回错误状态码
      // console.error('错误响应数据：', error.response.data);
      ElMessage.error(error.response.data.message || '分析请求失败，请检查输入数据格式');
    } else if (error.request) {
      // 请求发送成功但没有收到响应
      ElMessage.error('服务器无响应，请稍后重试');
    } else {
      // 请求配置出错
      ElMessage.error(error.message || '请求发送失败，请稍后重试');
    }
  } finally {
    loading.close();
  }
};
</script>