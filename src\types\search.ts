// 基础接口
export interface BaseSearchItem {
  type: 'webpage' | 'encyclopedia' | 'medical'
  name: string
  datePublished?: string
}

// 网页结果
export interface WebpageItem extends BaseSearchItem {
  type: 'webpage'
  url: string
  siteName: string
  snippet: string
}

// 百科卡片数据
interface BaikeCard {
  title: string
  subTitle?: string
  oriPicList?: string[]
  dynAbstract?: string
  baikeURL?: string
}

// 医疗子项数据
interface MedicalSubItem {
  title: string
  content: string
  doctorName: string
  doctorLevel: string
  hospital: string
  hospitalLv: string
  department?: string
  wapUrl4Resin: string
}

// 医疗卡片数据
interface MedicalCard {
  subitem: MedicalSubItem[]
}

// 百科数据结构
interface ModuleData {
  card?: BaikeCard
}

interface ModuleItem {
  data?: ModuleData
}

interface ModuleList {
  item_list?: ModuleItem[]
}

// 百科词条
export interface EncyclopediaItem extends BaseSearchItem {
  type: 'encyclopedia'
  modelCard: {
    module_list: Array<{
      item_list: Array<{
        data: {
          card: {
            dynAbstract: string
          }
        }
      }>
    }>
  }
}

// 医疗信息
export interface MedicalItem extends BaseSearchItem {
  type: 'medical'
  modelCard: {
    subitem: Array<{
      content: string
    }>
  }
}

// 联合类型
export type SearchItem = WebpageItem | EncyclopediaItem | MedicalItem

export interface SearchMessage {
  role: string
  content: SearchItem[]
}

export interface SearchResponse {
  messages: SearchMessage[]
} 