<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>域名验证阻止功能演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .demo-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .demo-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #dc3545;
            padding-bottom: 5px;
        }
        .error-case {
            background: #f8d7da;
            border-left: 4px solid #dc3545;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .warning-case {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success-case {
            background: #d4edda;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .test-input {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 8px 0;
        }
        .result-message {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            padding: 10px;
            border-radius: 5px;
            margin: 8px 0;
        }
        .highlight {
            background: #ffeb3b;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }
        .stop-icon {
            color: #dc3545;
            font-size: 20px;
            margin-right: 8px;
        }
        .warning-icon {
            color: #ffc107;
            font-size: 20px;
            margin-right: 8px;
        }
        .success-icon {
            color: #28a745;
            font-size: 20px;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <h1>🚫 域名验证阻止功能演示</h1>
    
    <div class="demo-section">
        <div class="demo-title">🎯 功能说明</div>
        <p>现在当include参数的值类型不符合要求时，系统会：</p>
        <ul>
            <li>🚫 <span class="highlight">阻止程序执行</span>：验证失败时不会发送API请求</li>
            <li>❌ <span class="highlight">显示错误消息</span>：使用ElMessage.error显示具体错误</li>
            <li>📝 <span class="highlight">控制台日志</span>：输出详细的验证失败信息</li>
            <li>⚠️ <span class="highlight">实时验证</span>：输入时实时检查但不阻止输入</li>
        </ul>
    </div>

    <div class="demo-section">
        <div class="demo-title">🚫 阻止执行的情况</div>
        
        <div class="error-case">
            <span class="stop-icon">🛑</span><strong>情况1：域名数量超过20个</strong>
            <div class="test-input">qq.com|163.com|sina.com|sohu.com|baidu.com|google.com|yahoo.com|bing.com|taobao.com|tmall.com|jd.com|weibo.com|zhihu.com|douban.com|bilibili.com|youku.com|iqiyi.com|tencent.com|alibaba.com|netease.com|xinhua.com</div>
            <div class="result-message">
                <strong>错误消息：</strong>域名数量超限：输入了21个域名，最多只能指定20个域名！<br>
                <strong>程序行为：</strong>搜索被阻止，不发送API请求
            </div>
        </div>
        
        <div class="error-case">
            <span class="stop-icon">🛑</span><strong>情况2：所有域名格式都无效</strong>
            <div class="test-input">.invalid.com|another.invalid.|.bad.format|invalid_domain|no-dot-domain</div>
            <div class="result-message">
                <strong>错误消息：</strong>域名格式错误：所有输入的域名格式都不正确！请检查域名格式。<br>
                <strong>程序行为：</strong>搜索被阻止，不发送API请求
            </div>
        </div>
        
        <div class="error-case">
            <span class="stop-icon">🛑</span><strong>情况3：域名包含非法字符</strong>
            <div class="test-input">qq .com|163@.com|sina#.com|sohu$.com</div>
            <div class="result-message">
                <strong>错误消息：</strong>域名格式错误：所有输入的域名格式都不正确！请检查域名格式。<br>
                <strong>程序行为：</strong>搜索被阻止，不发送API请求
            </div>
        </div>
    </div>

    <div class="demo-section">
        <div class="demo-title">⚠️ 警告但继续执行的情况</div>
        
        <div class="warning-case">
            <span class="warning-icon">⚠️</span><strong>情况1：部分域名无效</strong>
            <div class="test-input">.invalid.com|qq.com|another.invalid.|163.com|sina.com</div>
            <div class="result-message">
                <strong>警告消息：</strong>域名格式警告：2个无效域名已过滤，将使用3个有效域名继续搜索<br>
                <strong>程序行为：</strong>过滤无效域名，使用有效域名继续搜索
            </div>
        </div>
        
        <div class="warning-case">
            <span class="warning-icon">⚠️</span><strong>情况2：域名数量在限制内但有无效格式</strong>
            <div class="test-input">qq.com|.invalid.com|163.com|sina.com|bad_format</div>
            <div class="result-message">
                <strong>警告消息：</strong>域名格式警告：2个无效域名已过滤，将使用3个有效域名继续搜索<br>
                <strong>程序行为：</strong>过滤无效域名，使用有效域名继续搜索
            </div>
        </div>
    </div>

    <div class="demo-section">
        <div class="demo-title">✅ 正常执行的情况</div>
        
        <div class="success-case">
            <span class="success-icon">✅</span><strong>情况1：所有域名都有效</strong>
            <div class="test-input">qq.com|163.com|sina.com|sohu.com|baidu.com</div>
            <div class="result-message">
                <strong>程序行为：</strong>正常执行搜索，include参数值为：qq.com|163.com|sina.com|sohu.com|baidu.com
            </div>
        </div>
        
        <div class="success-case">
            <span class="success-icon">✅</span><strong>情况2：包含子域名</strong>
            <div class="test-input">news.qq.com|m.163.com|tech.sina.com|sports.sohu.com</div>
            <div class="result-message">
                <strong>程序行为：</strong>正常执行搜索，include参数值为：news.qq.com|m.163.com|tech.sina.com|sports.sohu.com
            </div>
        </div>
    </div>

    <div class="demo-section">
        <div class="demo-title">🧪 测试步骤</div>
        
        <div style="background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0;">
            <strong>步骤 1：</strong> 访问 <a href="http://localhost:5174/" target="_blank">http://localhost:5174/</a>
        </div>
        
        <div style="background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0;">
            <strong>步骤 2：</strong> 按F12打开开发者工具，切换到Console标签
        </div>
        
        <div style="background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0;">
            <strong>步骤 3：</strong> 开启"指定网站搜索"开关
        </div>
        
        <div style="background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0;">
            <strong>步骤 4：</strong> 测试阻止执行的情况
            <ul>
                <li>输入超过20个域名</li>
                <li>输入全部无效的域名格式</li>
                <li>观察ElMessage错误提示</li>
                <li>尝试点击"开始搜索"，验证搜索被阻止</li>
            </ul>
        </div>
        
        <div style="background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0;">
            <strong>步骤 5：</strong> 测试警告但继续执行的情况
            <ul>
                <li>输入部分无效域名</li>
                <li>观察ElMessage警告提示</li>
                <li>点击"开始搜索"，验证搜索正常执行</li>
            </ul>
        </div>
        
        <div style="background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0;">
            <strong>步骤 6：</strong> 观察控制台日志输出，查看详细的验证过程
        </div>
    </div>

    <div class="demo-section">
        <div class="demo-title">🔍 验证逻辑</div>
        <div style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
            <h4>验证规则：</h4>
            <ul>
                <li>域名数量：最多20个</li>
                <li>域名格式：必须包含点号，不含空格，长度3-100字符</li>
                <li>域名开头结尾：不能以点号开头或结尾</li>
            </ul>
            
            <h4>阻止条件：</h4>
            <ul>
                <li>🚫 域名数量超过20个</li>
                <li>🚫 所有域名格式都无效</li>
            </ul>
            
            <h4>警告条件：</h4>
            <ul>
                <li>⚠️ 部分域名格式无效（会过滤无效域名继续执行）</li>
            </ul>
        </div>
    </div>

    <div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <strong>🎉 功能特点：</strong>
        <ul>
            <li>✅ 严格验证：不符合要求时完全阻止程序执行</li>
            <li>✅ 智能过滤：部分无效时过滤后继续执行</li>
            <li>✅ 用户友好：清晰的错误提示和建议</li>
            <li>✅ 调试友好：详细的控制台日志</li>
        </ul>
    </div>
</body>
</html>
