<template>
  <div class="space-y-6" v-loading="loading">
    <div class="card-container">
      <h3 class="text-lg font-medium mb-4">AI 搜索问答</h3>
      <div class="flex space-x-4 gap-4 flex-wrap md:gap-0">
        <el-input v-model="aiQuery" placeholder="请输入您的问题" class="w-full mr-0 md:flex-1 md:mr-4" size="large">
          <template #prefix>
            <el-icon>
              <Search />
            </el-icon>
          </template>
        </el-input>
        <el-select v-model="aiFreshness" placeholder="时间筛选" style="width: 140px" size="large">
          <el-option v-for="option in freshnessOptions" :key="option.value" :label="option.label"
            :value="option.value" />
        </el-select>
        <el-button type="primary" size="large" class="!rounded-button whitespace-nowrap" @click="handleAiSearch"
          :loading="loading">
          开始搜索
        </el-button>
      </div>
    </div>

    <div class="card-container">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium">分析结果</h3>
        <span class="text-gray-500">共 {{ totalResults }} 条结果</span>
      </div>

      <div class="space-y-6">
        <template v-if="paginatedResults.length">
          <div v-for="(item, index) in paginatedResults" 
            :key="index"
            class="pb-6 border-b border-gray-100 last:border-none">
            <!-- 百科搜索结果卡片 -->
            <div v-if="item.type === 'encyclopedia'" class="baike-card bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow">
              <div class="flex items-center">
                <!-- 左侧图片 -->
                <div v-if="getItemCover(item)" class="w-48 h-36 flex-shrink-0">
                  <img :src="getItemCover(item)" :alt="item.name" class="w-full h-full object-cover rounded-l-lg">
                </div>
                <!-- 右侧内容 -->
                <div class="flex-1 p-4">
                  <div class="flex items-start justify-between">
                    <div>
                      <h3 class="text-xl font-medium text-blue-600 hover:text-blue-800">
                        <a :href="getItemLink(item)" target="_blank">{{ item.name }}</a>
                      </h3>
                      <p class="text-gray-600 text-sm mt-1">{{ getItemSubtitle(item) }}</p>
                    </div>
                    <span class="text-sm text-gray-500">{{ formatDate(item.datePublished) }}</span>
                  </div>
                  <p class="text-gray-700 mt-3 line-clamp-3">{{ getItemDescription(item) }}</p>
                  <div class="mt-3 flex items-center text-sm text-gray-500">
                    <span class="flex items-center">
                      <el-icon class="mr-1"><Collection /></el-icon>
                      百科信息
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <!-- 网页搜索结果卡片 -->
            <div v-else-if="item.type === 'webpage'" class="webpage-card">
              <h3 class="text-lg font-medium mb-2">
                <a :href="item.url" target="_blank" class="text-blue-600 hover:text-blue-800">
                  {{ getItemTitle(item) }}
                </a>
              </h3>
              <div class="text-sm text-gray-500 mb-2 flex items-center space-x-3">
                <span>{{ getItemSource(item) }}</span>
                <span class="text-gray-400">{{ formatDate(item.datePublished) }}</span>
              </div>
              <p class="text-gray-700 text-sm line-clamp-2">
                {{ getItemDescription(item) }}
              </p>
            </div>
            <!-- 医疗搜索结果卡片 -->
            <div v-else-if="item.type === 'medical'" class="space-y-4">
              <h3 class="text-xl font-medium text-gray-900">{{ item.name }}</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-4">
                <div v-for="(subItem, subIndex) in item.modelCard?.subitem" 
                  :key="subIndex" 
                  class="medical-card bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow p-4 border border-gray-100">
                  <h4 class="text-lg font-medium text-blue-600 mb-2">
                    <a :href="subItem.wapUrl4Resin" target="_blank">{{ subItem.title }}</a>
                  </h4>
                  <div class="text-sm text-gray-500 space-y-2 mb-3">
                    <div class="flex items-center justify-between">
                      <span class="font-medium text-indigo-600">{{ subItem.doctorName }}</span>
                      <span>{{ subItem.doctorLevel }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                      <span class="text-gray-600">{{ subItem.hospital }}</span>
                      <span class="text-xs px-2 py-1 bg-blue-50 text-blue-600 rounded">{{ subItem.hospitalLv }}</span>
                    </div>
                    <div v-if="subItem.department" class="text-gray-600">
                      科室：{{ subItem.department }}
                    </div>
                  </div>
                  <p class="text-gray-700 text-sm line-clamp-4">{{ subItem.content }}</p>
                </div>
              </div>
            </div>
          </div>
        </template>
        <el-empty v-else description="暂无搜索结果" />
      </div>

      <!-- 只有当网页结果超过10条时才显示分页 -->
      <div v-if="webpageTotal > pageSize" class="flex justify-center mt-6">
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="webpageTotal"
          :pager-count="7"
          layout="prev, pager, next"
          @current-change="handlePageChange"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import axios from 'axios'
import { Search, Calendar, Collection } from '@element-plus/icons-vue'
import type { 
  SearchItem, 
  SearchResponse, 
  WebpageItem, 
  EncyclopediaItem, 
  MedicalItem 
} from '../types/search'
import { ElMessage } from 'element-plus'

// 类型定义
interface FreshnessOption {
  label: string
  value: string
}

// 常量定义
const freshnessOptions: FreshnessOption[] = [
  { label: '不限', value: 'noLimit' },
  { label: '一天内', value: 'oneDay' },
  { label: '一周内', value: 'oneWeek' },
  { label: '一个月内', value: 'oneMonth' },
  { label: '一年内', value: 'oneYear' }
]

const API_URL = 'https://api.bochaai.com/v1/ai-search'
const API_KEY = 'sk-29cfe2690641438e9ec710f0c7e721eb'

// 响应式状态
const aiQuery = ref('')
const aiFreshness = ref('noLimit')
const loading = ref(false)
const aiSearchResults = ref<SearchItem[]>([])
const currentPage = ref(1)
const pageSize = ref(50)
const totalResults = ref(0)
const webpageTotal = ref(0)

// 存储不同类型的结果
const webpageResults = ref<WebpageItem[]>([])
const baikeResults = ref<EncyclopediaItem[]>([])
const medicalResults = ref<MedicalItem[]>([])

// 计算分页后的结果
const paginatedResults = computed(() => {
  // 医疗和百科结果直接显示
  const fixedResults = [...medicalResults.value, ...baikeResults.value]
  
  // 网页结果分页
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  const paginatedWebpageResults = webpageResults.value.slice(start, end)
  
  return [...fixedResults, ...paginatedWebpageResults]
})

// 工具函数
const getItemUrl = (item: SearchItem): string => {
  switch (item.type) {
    case 'encyclopedia':
      return item?.modelCard?.[0]?.item_list?.[0]?.data?.baikeURL ?? '#'
    case 'webpage':
      return item?.url ?? '#'
    case 'medical':
      return item?.url ?? '#'
    default:
      return '#'
  }
}

const getItemName = (item: SearchItem): string => {
  switch (item.type) {
    case 'encyclopedia':
      return item?.name ?? '未知标题'
    case 'webpage':
      return item?.title ?? '未知标题'
    case 'medical':
      return item?.title ?? '未知标题'
    default:
      return '未知标题'
  }
}

const getAbstractInfo = (item: SearchItem): string => {
  switch (item.type) {
    case 'encyclopedia':
      return item?.modelCard?.[0]?.item_list?.[0]?.data?.abstract_info ?? '暂无描述'
    case 'webpage':
      return item?.snippet ?? '暂无描述'
    case 'medical':
      return item?.summary ?? '暂无描述'
    default:
      return '暂无描述'
  }
}

const getItemDate = (item: SearchItem): string => {
  return item?.datePublished ?? '未知时间'
}

// 处理搜索结果
const processSearchResults = (data: SearchResponse) => {
  // 清空之前的结果
  webpageResults.value = []
  baikeResults.value = []
  medicalResults.value = []

  try {
    data.messages?.forEach((msg) => {
      let contentObj
      try {
        contentObj = typeof msg.content === 'string' ? JSON.parse(msg.content) : msg.content
        
        if (msg.content_type === 'webpage' && contentObj.value && Array.isArray(contentObj.value)) {
          // 处理网页搜索结果
          webpageResults.value.push(...contentObj.value.map(item => ({
            type: 'webpage',
            name: item.name,
            url: item.url,
            siteName: item.siteName || '未知来源',
            snippet: item.snippet || '暂无简介',
            datePublished: item.datePublished || new Date().toISOString()
          })))
        } else if (msg.content_type === 'baike_pro') {
          // 处理百科结果
          const items = Array.isArray(contentObj) ? contentObj : [contentObj]
          items.forEach((item: any) => {
            if (item.modelCard?.module_list?.[0]?.item_list?.[0]?.data?.card) {
              const card = item.modelCard.module_list[0].item_list[0].data.card
              baikeResults.value.push({
                type: 'encyclopedia',
                name: card.title || '',
                url: card.baikeURL || '',
                datePublished: new Date().toISOString(),
                modelCard: {
                  module_list: [{
                    item_list: [{
                      data: {
                        card: {
                          title: card.title || '',
                          subTitle: card.subTitle || '',
                          oriPicList: card.oriPicList || [],
                          dynAbstract: card.dynAbstract || '',
                          baikeURL: card.baikeURL || ''
                        }
                      }
                    }]
                  }]
                }
              })
            }
          })
        } else if (msg.content_type === 'medical_common') {
          // 处理医疗结果
          const items = Array.isArray(contentObj) ? contentObj : [contentObj]
          items.forEach((item: any) => {
            if (item.modelCard?.subitem) {
              medicalResults.value.push({
                type: 'medical',
                name: item.name || '',
                url: '',
                datePublished: item.datePublished || new Date().toISOString(),
                modelCard: {
                  subitem: item.modelCard.subitem.map((sub: any) => ({
                    title: sub.title || '',
                    content: sub.content || '',
                    doctorName: sub.doctorName || '',
                    doctorLevel: sub.doctorLevel || '',
                    hospital: sub.hospital || '',
                    hospitalLv: sub.hospitalLv || '',
                    department: sub.department || '',
                    wapUrl4Resin: sub.wapUrl4Resin || ''
                  }))
                }
              })
            }
          })
        }
      } catch (e) {
        console.error('解析content失败:', e)
      }
    })

    // 更新总数和分页信息
    webpageTotal.value = webpageResults.value.length
    currentPage.value = 1 // 重置页码

    // 使用计算属性的结果
    aiSearchResults.value = paginatedResults.value
    totalResults.value = medicalResults.value.length + baikeResults.value.length + webpageResults.value.length

    // console.log('医疗结果数：', medicalResults.value.length)
    // console.log('百科结果数：', baikeResults.value.length)
    // console.log('网页结果数：', webpageResults.value.length)
    // console.log('当前页网页结果：', paginatedResults.value.filter(item => item.type === 'webpage').length)
  } catch (e) {
    // console.error('处理搜索结果失败:', e)
    // 发生错误时清空所有结果
    webpageResults.value = []
    baikeResults.value = []
    medicalResults.value = []
    aiSearchResults.value = []
    totalResults.value = 0
    webpageTotal.value = 0
  }
}

// 获取标题
const getItemTitle = (item: SearchItem): string => {
  return item?.name ?? '未知标题'
}

// 获取描述
const getItemDescription = (item: SearchItem): string => {
  switch (item.type) {
    case 'encyclopedia':
      return item.modelCard?.module_list?.[0]?.item_list?.[0]?.data?.card?.dynAbstract || ''
    case 'webpage':
      return item?.snippet ?? '暂无描述'
    case 'medical':
      if (item.modelCard?.subitem?.[0]) {
        return item.modelCard.subitem[0].content
      }
      return '暂无描述'
    default:
      return '暂无描述'
  }
}

// 获取来源名称
const getItemSource = (item: SearchItem): string => {
  if (item.type === 'encyclopedia') {
    return '搜狗百科'
  }
  if (item.type === 'webpage') {
    return item.siteName || ''
  }
  return ''
}

// 搜索处理函数
const handleAiSearch = async () => {
  if (!aiQuery.value.trim()) {
    ElMessage.warning('请输入搜索内容')
    return
  }

  try {
    loading.value = true
    const response = await axios.post(API_URL, {
      query: aiQuery.value,
      freshness: aiFreshness.value,
      answer: false,
      count: pageSize.value,
      stream: false
    }, {
      headers: {
        'Authorization': API_KEY,
        'Content-Type': 'application/json',
        'Accept': '*/*'
      }
    })

    // 直接处理搜索结果
    processSearchResults(response.data)

  } catch (error) {
    // console.error('AI搜索请求失败:', error)
    // ElMessage.error('搜索失败，请稍后重试')
    // 清空结果
    webpageResults.value = []
    baikeResults.value = []
    medicalResults.value = []
    aiSearchResults.value = []
    totalResults.value = 0
    webpageTotal.value = 0
  } finally {
    loading.value = false
  }
}

// 键盘事件处理
const handleKeyPress = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    handleAiSearch()
  }
}

// 格式化日期函数
const formatDate = (dateString: string | undefined) => {
  if (!dateString) return ''
  try {
    const date = new Date(dateString)
    return date.toISOString().split('T')[0] // 转换为 YYYY-MM-DD 格式
  } catch (e) {
    return ''
  }
}

// 获取百科副标题
const getItemSubtitle = (item: SearchItem): string => {
  if (item.type === 'encyclopedia') {
    return item.modelCard?.module_list?.[0]?.item_list?.[0]?.data?.card?.subTitle || ''
  }
  return ''
}

// 获取百科封面图片
const getItemCover = (item: SearchItem): string => {
  if (item.type === 'encyclopedia') {
    return item.modelCard?.module_list?.[0]?.item_list?.[0]?.data?.card?.oriPicList?.[0] || ''
  }
  return ''
}

// 获取百科链接
const getItemLink = (item: SearchItem): string => {
  if (item.type === 'encyclopedia') {
    // 从原始数据结构中获取链接
    const card = item.modelCard?.module_list?.[0]?.item_list?.[0]?.data?.card
    return card?.baikeURL || item.url || ''
  }
  // 对于网页类型，返回原始url
  if (item.type === 'webpage') {
    return item.url || ''
  }
  return ''
}

// 处理页码变化
const handlePageChange = (page: number) => {
  currentPage.value = page
  // 滚动到顶部
  window.scrollTo({ top: 0, behavior: 'smooth' })
}

onMounted(() => {
  window.addEventListener('keypress', handleKeyPress)
})

onUnmounted(() => {
  window.removeEventListener('keypress', handleKeyPress)
})
</script>

<style scoped>
.card-container {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  padding: 1.5rem;
}

.baike-card {
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.baike-card:hover {
  border-color: #d1d5db;
}

.webpage-card {
  padding: 1rem;
  border-radius: 0.5rem;
  transition: background-color 0.2s;
}

.webpage-card:hover {
  background-color: #f9fafb;
}
</style>