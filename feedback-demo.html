<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索反馈功能演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .demo-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .demo-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        .feedback-type {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 10px 0;
        }
        .feedback-type.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .feedback-type.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        .feedback-type.success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .feedback-type.info {
            border-left-color: #17a2b8;
            background: #d1ecf1;
        }
        .test-scenario {
            background: #e3f2fd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }
        .message-preview {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 8px 0;
        }
        .highlight {
            background: #ffeb3b;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>🔔 搜索反馈功能演示</h1>
    
    <div class="demo-section">
        <div class="demo-title">📋 功能概述</div>
        <p>现在WebSearch组件会在各种情况下使用Element Plus的ElMessage显示反馈信息，包括：</p>
        <ul>
            <li>✅ 域名验证和处理反馈</li>
            <li>✅ 搜索结果统计反馈</li>
            <li>✅ 搜索失败错误反馈</li>
            <li>✅ 控制台详细日志输出</li>
        </ul>
    </div>

    <div class="demo-section">
        <div class="demo-title">🎯 域名验证反馈</div>
        
        <div class="feedback-type warning">
            <strong>域名数量超限</strong>
            <div class="message-preview">域名数量超限：输入了25个域名，已自动保留前20个</div>
            <div class="test-scenario">
                <strong>触发条件：</strong> 输入超过20个域名<br>
                <strong>测试输入：</strong> <code>qq.com|163.com|sina.com|...(共25个域名)</code>
            </div>
        </div>
        
        <div class="feedback-type warning">
            <strong>域名格式错误</strong>
            <div class="message-preview">域名格式错误：2个无效域名已过滤，有效域名：3个</div>
            <div class="test-scenario">
                <strong>触发条件：</strong> 输入格式不正确的域名<br>
                <strong>测试输入：</strong> <code>.invalid.com|qq.com|another.invalid.|163.com</code>
            </div>
        </div>
    </div>

    <div class="demo-section">
        <div class="demo-title">📊 搜索结果反馈</div>
        
        <div class="feedback-type warning">
            <strong>无搜索结果</strong>
            <div class="message-preview">指定网站搜索无结果：在"qq.com|163.com"中未找到相关内容，建议更换关键词或添加更多网站</div>
            <div class="test-scenario">
                <strong>触发条件：</strong> 指定网站搜索返回0条结果<br>
                <strong>测试方法：</strong> 搜索一个在指定网站中不存在的内容
            </div>
        </div>
        
        <div class="feedback-type info">
            <strong>结果较少</strong>
            <div class="message-preview">指定网站搜索结果较少：仅找到3条结果，可尝试添加更多相关网站域名</div>
            <div class="test-scenario">
                <strong>触发条件：</strong> 指定网站搜索返回少于5条结果<br>
                <strong>测试方法：</strong> 搜索一个相对冷门的关键词
            </div>
        </div>
        
        <div class="feedback-type success">
            <strong>搜索成功</strong>
            <div class="message-preview">指定网站搜索成功：在"qq.com|163.com"中找到10条相关结果</div>
            <div class="test-scenario">
                <strong>触发条件：</strong> 指定网站搜索返回5条或以上结果<br>
                <strong>测试方法：</strong> 搜索一个热门关键词
            </div>
        </div>
    </div>

    <div class="demo-section">
        <div class="demo-title">❌ 搜索失败反馈</div>
        
        <div class="feedback-type error">
            <strong>指定网站搜索失败</strong>
            <div class="message-preview">指定网站搜索失败：网络错误 (500)</div>
            <div class="test-scenario">
                <strong>触发条件：</strong> API请求失败（网络错误、服务器错误等）<br>
                <strong>错误类型：</strong> 400(参数错误)、401(密钥无效)、403(访问拒绝)、404(服务不可用)、429(请求频繁)、500(服务器错误)
            </div>
        </div>
        
        <div class="feedback-type error">
            <strong>批量搜索失败</strong>
            <div class="message-preview">批量指定网站搜索失败：请求过于频繁，请稍后再试</div>
            <div class="test-scenario">
                <strong>触发条件：</strong> 批量搜索时API请求失败<br>
                <strong>特点：</strong> 会显示具体的错误原因和建议
            </div>
        </div>
    </div>

    <div class="demo-section">
        <div class="demo-title">🧪 测试步骤</div>
        
        <div class="test-scenario">
            <strong>步骤 1：</strong> 访问 <a href="http://localhost:5174/" target="_blank">http://localhost:5174/</a>
        </div>
        
        <div class="test-scenario">
            <strong>步骤 2：</strong> 开启"指定网站搜索"开关
        </div>
        
        <div class="test-scenario">
            <strong>步骤 3：</strong> 测试域名验证反馈
            <ul>
                <li>输入超过20个域名：<code>qq.com|163.com|sina.com|sohu.com|...(继续添加)</code></li>
                <li>输入无效域名：<code>.invalid.com|qq.com|another.invalid.</code></li>
            </ul>
        </div>
        
        <div class="test-scenario">
            <strong>步骤 4：</strong> 测试搜索结果反馈
            <ul>
                <li>输入有效域名：<code>qq.com|163.com</code></li>
                <li>搜索热门关键词：<code>人工智能</code> (应该显示成功消息)</li>
                <li>搜索冷门关键词：<code>极其罕见的词汇组合</code> (可能显示结果较少)</li>
            </ul>
        </div>
        
        <div class="test-scenario">
            <strong>步骤 5：</strong> 观察页面右上角的ElMessage提示和控制台日志
        </div>
    </div>

    <div class="demo-section">
        <div class="demo-title">🔍 控制台日志</div>
        <p>除了ElMessage提示外，控制台还会显示详细的调试信息：</p>
        <div class="message-preview">
🔍 开始处理域名: qq.com|163.com<br>
📋 分割后的域名列表: ["qq.com", "163.com"]<br>
🔍 验证域名 "qq.com": ✅ 有效<br>
🔍 验证域名 "163.com": ✅ 有效<br>
✅ 有效域名列表: ["qq.com", "163.com"]<br>
🎯 最终处理结果: qq.com|163.com<br>
🔍 指定网站搜索结果统计:<br>
📊 总结果数: 1234<br>
📄 当前页结果数: 10
        </div>
    </div>

    <div class="demo-section">
        <div class="demo-title">✨ 功能特点</div>
        <ul>
            <li>🎯 <span class="highlight">智能反馈</span>：根据不同情况显示相应的消息类型</li>
            <li>📝 <span class="highlight">详细信息</span>：提供具体的错误原因和建议</li>
            <li>🔍 <span class="highlight">调试友好</span>：控制台输出详细的处理过程</li>
            <li>🎨 <span class="highlight">用户友好</span>：使用Element Plus统一的消息样式</li>
            <li>⚡ <span class="highlight">实时反馈</span>：操作即时显示相应提示</li>
        </ul>
    </div>
</body>
</html>
