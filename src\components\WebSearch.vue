<template>
  <div class="space-y-6" v-loading="loading">
    <div class="card-container">
      <div class="flex items-center space-x-4 mb-6">
        <el-radio-group v-model="searchType" class="flex items-center space-x-4">
          <el-radio :value="0">单项搜索</el-radio>
          <el-radio :value="1">批量搜索</el-radio>
        </el-radio-group>
      </div>
      
      <div class="relative">
        <el-input v-model="searchKeyword" :type="searchType === 0 ? 'text' : 'textarea'"
          :rows="searchType === 1 ? 4 : 1" :placeholder="searchType === 0 ? '请输入搜索内容' : '请输入多个搜索内容，每行一个'"
          class="!rounded-lg" size="large" @keydown="handleKeyDown" style="margin-bottom: 1rem;">
          <template #prefix>
            <el-icon>
              <Search />
            </el-icon>
          </template>
        </el-input>

        <el-switch
          v-model="enableSiteSearch"
          class="mb-2"
          active-text="指定网站搜索"
          size="small"
        />
        <el-input v-if="enableSiteSearch" v-model="includeWebsites" type="textarea"
          placeholder="多个域名使用 | 或 , 分隔，最多20个域名。例如：qq.com|m.163.com"
          class="!rounded-lg mt-2" size="large" @keydown.enter="handleSearch">
          <template #prefix>
            <el-icon>
              <Link />
            </el-icon>
          </template>
        </el-input>
      </div>
      <div class="flex items-center space-x-4 mt-4">
        <div class="w-[200px]">
          <el-select v-model="searchFilter" placeholder="时间筛选" size="large">
            <el-option label="不限" value="noLimit" />
            <el-option label="一天内" value="oneDay" />
            <el-option label="一周内" value="oneWeek" />
            <el-option label="一个月内" value="oneMonth" />
            <el-option label="一年内" value="oneYear" />
            <el-option label="指定日期" value="specificDate" />
            <el-option label="日期范围" value="dateRange" />
          </el-select>
        </div>
        <div class="w-56" v-if="searchFilter === 'specificDate'">
          <el-date-picker v-model="specificDate" type="date" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
            placeholder="选择日期" :disabled-date="disabledFutureDate" size="large"/>
        </div>

        <div class="w-72" v-if="searchFilter === 'dateRange'">
          <el-date-picker v-model="dateRange" type="daterange"
            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 300px" :disabled-date="disabledFutureDate" size="large" />
        </div>

        <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="handleSearch" size="large">
          开始搜索
        </el-button>
      </div>
    </div>
    <div class="card-container">
      <div class="flex justify-between items-center mb-4" v-if="searchResults.length">
        <h3 class="text-lg font-medium">搜索结果</h3>
        <span class="text-gray-500">共 {{ (total).toLocaleString() }} 条结果</span>
      </div>
      <div class="space-y-6">
        <el-skeleton v-if="loading" :rows=5 animated />
        <template v-else>
          <el-empty v-if="!searchResults.length" description="暂无数据" />
          <div v-else v-for="(item, index) in searchResults" :key="index"
            class="pb-6 border-b border-gray-100 last:border-none">
            <a :href="item.url" target="_blank" class="text-lg font-medium text-blue-600 hover:text-blue-700 cursor-pointer mb-2 line-clamp-1">
              {{ item.title }}
            </a>
            <div class="text-gray-500 text-sm mb-2 flex items-center gap-x-4">
              <div class="flex items-center gap-x-1 text-gray-500 text-sm">
                <el-icon>
                  <Link />
                </el-icon>
                来源：{{ item.source }} 

              </div>
              <div class="flex items-center gap-x-2 text-gray-500 text-sm">
                <el-icon>
                  <Calendar />
                </el-icon> {{ item.date }}
              </div>
            </div>
            <p class="text-gray-600 line-clamp-3">
              {{ item.content }}
            </p>
          </div>
        </template>
      </div>
      <!-- <div class="flex justify-center mt-6">
        <div class=""  v-if="searchResults.length && total > 10">
          <el-pagination v-model:current-page="currentPage" :page-size="50" :total="total" layout="prev, pager, next" @current-change="handlePageChange" />
        </div>
        <el-button v-if="!searchResults.length && currentPage > 1" type="primary" class="ml-4" @click="currentPage = 1; handlePageChange(1)">返回第一页</el-button>
      </div> -->
    </div>

    <!-- 验证提示区域 -->
    <div v-if="showValidationTips" class="validation-tips-container">
      <el-alert
        v-for="(tip, index) in validationTips"
        :key="index"
        :title="tip.title"
        :type="tip.type"
        :description="tip.description"
        :closable="tip.closable"
        show-icon
        class="validation-tip"
        @close="removeValidationTip(index)"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref,  watch } from "vue";
import axios from "axios";
import { ElMessage } from 'element-plus'

const enableSiteSearch = ref(false)
const includeWebsites = ref("")

// 验证提示相关变量
const showValidationTips = ref(false)
// const validationTips = ref<Array<{
//   title: string;
//   type: 'success' | 'warning' | 'info' | 'error';
//   description: string;
//   closable: boolean;
// }>>([])

// 添加验证提示
// const addValidationTip = (title: string, type: 'success' | 'warning' | 'info' | 'error', description: string, closable = true) => {
//   validationTips.value.push({ title, type, description, closable });
//   showValidationTips.value = true;

//   // 自动清除提示（除了错误类型）
//   if (type !== 'error') {
//     setTimeout(() => {
//       removeValidationTip(validationTips.value.length - 1);
//     }, 5000);
//   }
// };

// 移除验证提示
const removeValidationTip = (index: number) => {
  validationTips.value.splice(index, 1);
  if (validationTips.value.length === 0) {
    showValidationTips.value = false;
  }
};


// 添加防抖函数
const debounce = (fn: Function, delay: number) => {
  let timer: number | undefined = undefined;
  return (...args: any[]) => {
    if (timer) window.clearTimeout(timer);
    timer = window.setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  };
};

interface SearchResult {
  title: string;
  url: string;
  source: string;
  date: string;
  content: string;
}

interface APIWebPage {
  name?: string;
  title?: string;
  url?: string;
  siteName?: string;
  datePublished?: string;
  date?: string;
  publishedDate?: string;
  snippet?: string;
  description?: string;
  content?: string;
}

interface APIWebPages {
  totalEstimatedMatches?: number;
  value?: APIWebPage[];
}

interface APIResponse {
  code: number;
  data: {
    webPages: APIWebPages;
  };
  webPages?: APIWebPages;  // 兼容直接返回在顶层的情况
}

const searchType = ref(0);

watch(searchType, () => {
  currentPage.value = 1;
  searchResults.value = [];
  total.value = 0;
});
const searchKeyword = ref("");
const searchFilter = ref("noLimit");
const currentPage = ref(1);
const loading = ref(false);
const total = ref(0);
const specificDate = ref("");
const formatDatePicker = (date: any) => {
  if (!date) return '';
  const d = new Date(date);
  return `${d.getFullYear()}-${(d.getMonth() + 1).toString().padStart(2, '0')}-${d.getDate().toString().padStart(2, '0')}`;
};
const dateRange = ref<[Date, Date] | []>([]);
const searchResults = ref<SearchResult[]>([]);

// 验证和处理域名函数
const validateAndProcessDomains = (domains: string): string | null => {
  console.log('🔍 开始处理域名:', domains);

  if (!domains.trim()) {
    console.log('⚠️ 域名输入为空');
    return '';
  }

  // 使用|或,分隔域名
  const domainList = domains.split(/[|,]/).map(d => d.trim()).filter(d => d);
  console.log('📋 分割后的域名列表:', domainList);

  // 限制最多20个域名
  if (domainList.length > 20) {
    // 显示前端错误提示
    ElMessage.error(` 域名数量超限: ${domainList.length}，最多 20 个域名`);
    // @ts-ignore
    window.ElMessage && window.ElMessage.error(`域名数量超限：输入了${domainList.length}个域名，最多只能指定20个域名！`);

    return null; // 返回null表示验证失败，阻止程序继续执行
  }

  // 简化的域名格式验证 - 更宽松的规则
  const validDomains = domainList.filter(domain => {
    // 基本检查：包含至少一个点，不包含空格，长度合理
    const isValid = domain.includes('.') &&
                   !domain.includes(' ') &&
                   domain.length > 3 &&
                   domain.length < 100 &&
                   !domain.startsWith('.') &&
                   !domain.endsWith('.');

    console.log(`🔍 验证域名 "${domain}": ${isValid ? '✅ 有效' : '❌ 无效'}`);
    return isValid;
  });

  console.log('✅ 有效域名列表:', validDomains);

  // 如果所有域名都无效，报错并阻止执行
  if (validDomains.length === 0) {
    const invalidDomains = domainList.filter(d => !validDomains.includes(d));

    ElMessage.error(`域名格式错误：正确示例：qq.com, news.163.com`);
    // @ts-ignore
    window.ElMessage && window.ElMessage.error(`域名格式错误：所有输入的域名格式都不正确！请检查域名格式。`);
    
    return null; // 返回null表示验证失败，阻止程序继续执行
  }

  // 如果部分域名无效，给出警告但继续执行
  if (validDomains.length !== domainList.length) {
    const invalidDomains = domainList.filter(d => !validDomains.includes(d));

    ElMessage.error(` 无效域名:', ${invalidDomains}`);
    // @ts-ignore
    window.ElMessage && window.ElMessage.warning(`域名格式警告：${invalidDomains.length}个无效域名已过滤，将使用${validDomains.length}个有效域名继续搜索`);
  }

  const result = validDomains.join('|');
  return result;
};

// 格式化日期函数
const formatDate = (dateString: any) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
};

// API请求函数
async function searchAPI(query: string, page = 1): Promise<APIResponse> {
  try {
    let freshnessValue = searchFilter.value;
    if (searchFilter.value === 'specificDate' && specificDate.value) {
      freshnessValue = specificDate.value;
    } else if (searchFilter.value === 'dateRange' && dateRange.value && dateRange.value.length === 2) {
      const startDate = formatDatePicker(dateRange.value[0]);
      const endDate = formatDatePicker(dateRange.value[1]);
      freshnessValue = `${startDate}..${endDate}`;
    }

    // 构建请求参数
    const requestParams: any = {
      query,
      freshness: freshnessValue,
      summary: false,
      count: 50,
      page
    };

    // 如果启用了指定网站搜索，添加include参数
    if (enableSiteSearch.value && includeWebsites.value.trim()) {
      const processedDomains = validateAndProcessDomains(includeWebsites.value);

      // 如果域名验证失败（返回null），抛出错误阻止请求执行
      if (processedDomains === null) {
        throw new Error('域名验证失败：include参数值类型不符合要求');
      }

      // 如果有有效域名，添加到请求参数中
      if (processedDomains) {
        requestParams.include = processedDomains;
      }
    }

    const response = await axios.post<APIResponse>('https://api.bochaai.com/v1/web-search',
      requestParams,
      {
        headers: {
          'Authorization': 'sk-29cfe2690641438e9ec710f0c7e721eb',
          'Content-Type': 'application/json'
        }
      }
    );

    // 打印请求参数和响应数据
    console.log('=== 搜索请求参数 ===');
    console.log(requestParams);

    // 如果启用了指定网站搜索，特别标注
    // if (enableSiteSearch.value && includeWebsites.value.trim()) {
    //   console.log('🎯 指定网站搜索已启用');
    //   console.log('� 原始域名输入:', includeWebsites.value);
    //   if (requestParams.include) {
    //     console.log('�📍 处理后的指定域名:', requestParams.include);
    //   } else {
    //     console.log('⚠️ 域名处理后为空，可能格式不正确');
    //   }
    // }

    // console.log('=== 搜索响应数据 ===');
    // console.log(response.data);

    return response.data;
  } catch (error) {
    // console.error('搜索请求失败:', error);
    throw error;
  }
}

const disabledFutureDate = (time: Date) => {
  return time.getTime() > Date.now();
};



// 获取错误信息
const getErrorMessage = (error: any): string => {
  if (error?.response?.data?.message) {
    return error.response.data.message;
  }
  if (error?.response?.status) {
    const status = error.response.status;
    switch (status) {
      case 400:
        return '请求参数错误，请检查搜索条件';
      case 401:
        return 'API密钥无效或已过期';
      case 403:
        return '访问被拒绝，请检查权限';
      case 404:
        return '搜索服务不可用';
      case 429:
        return '请求过于频繁，请稍后再试';
      case 500:
        return '服务器内部错误';
      default:
        return `网络错误 (${status})`;
    }
  }
  if (error?.message) {
    return error.message;
  }
  return '未知错误，请稍后重试';
};


const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && !event.shiftKey && searchType.value === 0) {
    event.preventDefault();
    handleSearch();
  }
};

// 修改日期监听和搜索触发逻辑
watch(searchFilter, (newVal) => {
  // 当切换到非日期选择时，自动触发搜索
  if (newVal !== 'specificDate' && newVal !== 'dateRange') {
    if (searchKeyword.value.trim()) {
      currentPage.value = 1;
      handleSearch();
    }
  }
});

// 监听具体日期变化
watch(specificDate, (newVal) => {
  if (searchFilter.value === 'specificDate' && newVal && searchKeyword.value.trim()) {
    currentPage.value = 1;
    handleSearch();
  }
});

// 监听日期范围变化
watch(dateRange, (newVal) => {
  if (searchFilter.value === 'dateRange' && newVal && newVal.length === 2 && searchKeyword.value.trim()) {
    currentPage.value = 1;
    handleSearch();
  }
});

// 监听指定网站搜索开关变化
watch(enableSiteSearch, (newVal) => {
  console.log(`🔄 指定网站搜索开关状态变化: ${newVal ? '开启' : '关闭'}`);

  if (!newVal) {
    includeWebsites.value = '';
    console.log('🧹 已清空域名输入');
  }
  // 如果有搜索内容且关闭了指定网站搜索，重新搜索
  if (!newVal && searchKeyword.value.trim()) {
    console.log('🔄 关闭指定网站搜索，重新执行普通搜索');
    currentPage.value = 1;
    handleSearch();
  }
});



// 修改单项搜索处理函数
const handleSingleSearch = async () => {
  if (!searchKeyword.value.trim()) {
    // 显示前端警告提示
    // addValidationTip(
    //   '搜索内容为空',
    //   'warning',
    //   '请输入要搜索的关键词。\n\n提示：\n• 单项搜索：输入一个关键词\n• 批量搜索：每行输入一个关键词',
    //   true
    // );
    ElMessage({
      message: '搜索内容为空，请检查输入格式',
      type: 'warning',
    });

    // @ts-ignore
    window.ElMessage && window.ElMessage.warning('请输入搜索内容');
    return;
  }

  // 如果启用了指定网站搜索，先验证域名
  if (enableSiteSearch.value && includeWebsites.value.trim()) {
    const processedDomains = validateAndProcessDomains(includeWebsites.value);
    if (processedDomains === null) {
      // 域名验证失败，直接返回，不执行搜索
      return;
    }
  }

  try {
    loading.value = true;
    const data = await searchAPI(searchKeyword.value, currentPage.value);
    const webPages = data.webPages || data.data?.webPages;
    const result = webPages?.value || [];
    total.value = webPages?.totalEstimatedMatches || 0;

    const mappedResult = result.map(item => ({
      title: item.name || item.title || '',
      url: item.url || '',
      source: item.siteName || '',
      date: formatDate(item.datePublished || item.date || item.publishedDate || ''),
      content: item.snippet || item.description || item.content || ''
    }));
    searchResults.value = mappedResult;
  } catch (error) {
    // 显示详细的错误反馈
    const errorMessage = getErrorMessage(error);

    if (enableSiteSearch.value && includeWebsites.value.trim()) {
      // 显示前端错误提示

      ElMessage.error(` 搜索请求失败：: ${errorMessage}`);
      // @ts-ignore
      window.ElMessage && window.ElMessage.error(`指定网站搜索失败：${errorMessage}`);

    } else {
      // 显示前端错误提示
      ElMessage.error(` 搜索请求失败：: ${errorMessage}`);

      // @ts-ignore
      window.ElMessage && window.ElMessage.error(`搜索失败：${errorMessage}`);
    }

    searchResults.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 修改批量搜索处理函数
const handleBatchSearch = async () => {
  const queries = searchKeyword.value
    .split('\n')
    .map(q => q.trim())
    .filter(q => q);

  if (queries.length === 0) {
    // 显示前端警告提示
    // addValidationTip(
    //   '批量搜索内容为空',
    //   'warning',
    //   '',
    //   true
    // );

    ElMessage({
      message: '批量搜索内容为空，请检查输入格式',
      type: 'warning',
    });

    // @ts-ignore
    window.ElMessage && window.ElMessage.warning('请输入搜索内容');
    return;
  }

  // 如果启用了指定网站搜索，先验证域名
  if (enableSiteSearch.value && includeWebsites.value.trim()) {
    const processedDomains = validateAndProcessDomains(includeWebsites.value);
    if (processedDomains === null) {
      // 域名验证失败，直接返回，不执行搜索
      ElMessage.error(` 域名验证失败，批量搜索被阻止`);
      return;
    }
  }

  try {
    loading.value = true;
    let allResults: SearchResult[] = [];
    let totalCount = 0;

    // 如果启用了指定网站搜索，打印批量搜索信息

    for (const query of queries) {
      const data = await searchAPI(query, 1);
      const webPages = data.webPages || data.data?.webPages;
      const result = webPages?.value || [];

      const mapped = result.map(item => ({
        title: item.name || item.title || '',
        url: item.url || '',
        source: item.siteName || '',
        date: formatDate(item.datePublished || item.date || item.publishedDate || ''),
        content: item.snippet || item.description || item.content || ''
      }));

      allResults = allResults.concat(mapped);
      totalCount += webPages?.totalEstimatedMatches || 0;
    }


    searchResults.value = allResults;
    total.value = totalCount;
  } catch (error) {
    // 显示详细的错误反馈
    const errorMessage = getErrorMessage(error);
    if (enableSiteSearch.value && includeWebsites.value.trim()) {
      // @ts-ignore
      window.ElMessage && window.ElMessage.error(`批量指定网站搜索失败：${errorMessage}`);
      console.log('🚨 批量指定网站搜索错误详情:', {
        error,
        websites: includeWebsites.value,
        keywords: queries
      });
    } else {
      // @ts-ignore
      window.ElMessage && window.ElMessage.error(`批量搜索失败：${errorMessage}`);
      console.log('🚨 批量搜索错误详情:', { error, keywords: queries });
    }

    searchResults.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 防抖优化后的搜索处理
const debouncedSearch = debounce(async () => {
  if (searchFilter.value === 'specificDate') {
    console.log('指定日期:', specificDate.value);
  }
  if (searchType.value === 0) {
    await handleSingleSearch();
  } else {
    await handleBatchSearch();
  }
}, 300);

const handleSearch = () => {
  if (!searchKeyword.value.trim()) {
    // 显示前端警告提示
    // addValidationTip(
    //   '搜索内容为空',
    //   'warning',
    //   '请输入要搜索的关键词。\n\n搜索模式：\n• 单项搜索：输入一个关键词\n• 批量搜索：每行输入一个关键词',
    //   true
    // );

    ElMessage({
      message: '搜索内容为空，请检查输入格式',
      type: 'warning',
    })

    // @ts-ignore
    window.ElMessage && window.ElMessage.warning('请输入搜索内容');
    return;
  }
  debouncedSearch();
};

const handlePageChange = async (page: number) => {
  currentPage.value = page;
  searchResults.value = []; // 清空之前的结果
  handleSearch();
  if (!searchResults.value.length && page > 1) {
    // @ts-ignore
    window.ElMessage && window.ElMessage.warning('当前页无数据');
  }
};

// {
//     code:200,
//     data:{
//         images:{
//             value:[
//             {
//                 thumbnailUrl:"http://www.ju1.cn/Public/Home/index/images/ai1.jpg",
//                 height:200,
//                 width:200,
//                 hostPageUrl:"https://m.yxwoo.com/ghjt/118429.html",
//                 width:200,
//                 thumbnailUrl:"https://www.yxwoo.com/uploads/images/20231212/20231212093731_45348.png",
//             }
//             ]
//         },
//         queryContext: {originalQuery: '句易网'},
//         webPages: {
//             totalEstimatedMatches: 793886,
//             value: [
//                 {
//                     name: '句易网',
//                     url: 'http://m.ju1.cn/index.php',
//                     datePublished: "2025-01-01T00:00:00+08:00",
//                     snippet: "句易网为您提供2025年最新广告法淘宝抖音违禁词在线过滤工具，欢迎使用，工具适用于各类行业自媒体短视频文案新闻稿检查，词库包含各类禁语极限用语，不断完善中."
//                 } 
//             ]
//         }
//     }
// }

</script>
<style scoped>
  .card-container{
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
  }

  .validation-tips-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 400px;
    width: 100%;
  }

  .validation-tip {
    margin-bottom: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
  }

  .validation-tip :deep(.el-alert__content) {
    white-space: pre-line;
    line-height: 1.5;
  }

  .validation-tip :deep(.el-alert__description) {
    margin-top: 8px;
    font-size: 13px;
    line-height: 1.4;
  }

  @media (max-width: 768px) {
    .validation-tips-container {
      top: 10px;
      right: 10px;
      left: 10px;
      max-width: none;
    }
  }
</style>