<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端提示组件演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .demo-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .demo-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        .feature-card {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .feature-card.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .feature-card.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        .feature-card.success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .feature-card.info {
            border-left-color: #17a2b8;
            background: #d1ecf1;
        }
        .test-input {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 8px 0;
        }
        .preview-box {
            border: 2px dashed #007bff;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            background: #f8f9fa;
        }
        .highlight {
            background: #ffeb3b;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }
        .icon {
            font-size: 20px;
            margin-right: 8px;
        }
        .position-demo {
            position: relative;
            height: 200px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            overflow: hidden;
        }
        .position-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.9);
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <h1>🎨 前端提示组件演示</h1>
    
    <div class="demo-section">
        <div class="demo-title">✨ 功能特点</div>
        <p>现在所有的验证和反馈信息都会在前端界面上显示，而不仅仅是控制台打印：</p>
        <ul>
            <li>🎯 <span class="highlight">固定位置显示</span>：提示框固定在页面右上角，不影响主要内容</li>
            <li>🎨 <span class="highlight">美观的UI</span>：使用Element Plus的Alert组件，样式统一美观</li>
            <li>⏰ <span class="highlight">自动消失</span>：成功、警告、信息类提示5秒后自动消失</li>
            <li>❌ <span class="highlight">手动关闭</span>：错误类提示需要手动关闭，确保用户看到</li>
            <li>📱 <span class="highlight">响应式设计</span>：在移动设备上自适应显示</li>
        </ul>
    </div>

    <div class="demo-section">
        <div class="demo-title">📍 显示位置</div>
        <div class="position-demo">
            <div class="position-indicator">
                提示框显示位置<br>
                (右上角固定)
            </div>
        </div>
        <p><strong>位置特点：</strong></p>
        <ul>
            <li>固定在页面右上角，z-index: 9999</li>
            <li>最大宽度400px，在移动设备上自适应</li>
            <li>多个提示会垂直堆叠显示</li>
            <li>不会遮挡主要内容区域</li>
        </ul>
    </div>

    <div class="demo-section">
        <div class="demo-title">🚫 错误类型提示</div>
        
        <div class="feature-card error">
            <span class="icon">🛑</span><strong>域名数量超限</strong>
            <div class="test-input">输入21个域名触发</div>
            <div class="preview-box">
                <strong>标题：</strong>域名数量超限<br>
                <strong>类型：</strong>error (红色)<br>
                <strong>内容：</strong>您输入了 21 个域名，系统最多只能处理 20 个域名。请减少域名数量后重试。<br>
                <strong>行为：</strong>需要手动关闭，阻止搜索执行
            </div>
        </div>
        
        <div class="feature-card error">
            <span class="icon">🛑</span><strong>域名格式错误</strong>
            <div class="test-input">.invalid.com|bad.format</div>
            <div class="preview-box">
                <strong>标题：</strong>域名格式错误<br>
                <strong>类型：</strong>error (红色)<br>
                <strong>内容：</strong>所有输入的域名格式都不正确！包含详细的格式要求和示例<br>
                <strong>行为：</strong>需要手动关闭，阻止搜索执行
            </div>
        </div>
        
        <div class="feature-card error">
            <span class="icon">🛑</span><strong>搜索失败</strong>
            <div class="preview-box">
                <strong>标题：</strong>指定网站搜索失败<br>
                <strong>类型：</strong>error (红色)<br>
                <strong>内容：</strong>包含具体错误信息、搜索关键词、指定网站等详细信息<br>
                <strong>行为：</strong>需要手动关闭
            </div>
        </div>
    </div>

    <div class="demo-section">
        <div class="demo-title">⚠️ 警告类型提示</div>
        
        <div class="feature-card warning">
            <span class="icon">⚠️</span><strong>部分域名无效</strong>
            <div class="test-input">qq.com|.invalid.com|163.com</div>
            <div class="preview-box">
                <strong>标题：</strong>域名格式警告<br>
                <strong>类型：</strong>warning (橙色)<br>
                <strong>内容：</strong>显示无效域名列表和有效域名列表<br>
                <strong>行为：</strong>5秒后自动消失，继续执行搜索
            </div>
        </div>
        
        <div class="feature-card warning">
            <span class="icon">⚠️</span><strong>搜索无结果</strong>
            <div class="preview-box">
                <strong>标题：</strong>指定网站搜索无结果<br>
                <strong>类型：</strong>warning (橙色)<br>
                <strong>内容：</strong>包含搜索建议和优化提示<br>
                <strong>行为：</strong>5秒后自动消失
            </div>
        </div>
    </div>

    <div class="demo-section">
        <div class="demo-title">ℹ️ 信息类型提示</div>
        
        <div class="feature-card info">
            <span class="icon">ℹ️</span><strong>搜索结果较少</strong>
            <div class="preview-box">
                <strong>标题：</strong>指定网站搜索结果较少<br>
                <strong>类型：</strong>info (蓝色)<br>
                <strong>内容：</strong>结果数量和优化建议<br>
                <strong>行为：</strong>5秒后自动消失
            </div>
        </div>
    </div>

    <div class="demo-section">
        <div class="demo-title">✅ 成功类型提示</div>
        
        <div class="feature-card success">
            <span class="icon">✅</span><strong>搜索成功</strong>
            <div class="preview-box">
                <strong>标题：</strong>指定网站搜索成功<br>
                <strong>类型：</strong>success (绿色)<br>
                <strong>内容：</strong>成功找到的结果数量<br>
                <strong>行为：</strong>5秒后自动消失
            </div>
        </div>
    </div>

    <div class="demo-section">
        <div class="demo-title">🧪 测试步骤</div>
        
        <div style="background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0;">
            <strong>步骤 1：</strong> 访问 <a href="http://localhost:5174/" target="_blank">http://localhost:5174/</a>
        </div>
        
        <div style="background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0;">
            <strong>步骤 2：</strong> 开启"指定网站搜索"开关
        </div>
        
        <div style="background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0;">
            <strong>步骤 3：</strong> 测试各种情况
            <ul>
                <li>输入超过20个域名 → 看到红色错误提示</li>
                <li>输入无效域名格式 → 看到红色错误提示</li>
                <li>输入部分无效域名 → 看到橙色警告提示</li>
                <li>正常搜索 → 看到相应的结果提示</li>
            </ul>
        </div>
        
        <div style="background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0;">
            <strong>步骤 4：</strong> 观察提示框的行为
            <ul>
                <li>错误提示需要手动点击关闭</li>
                <li>其他提示5秒后自动消失</li>
                <li>多个提示会堆叠显示</li>
                <li>开始新搜索时会清除之前的提示</li>
            </ul>
        </div>
    </div>

    <div class="demo-section">
        <div class="demo-title">🎨 UI特点</div>
        <div style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
            <h4>视觉设计：</h4>
            <ul>
                <li>使用Element Plus的Alert组件，保持UI一致性</li>
                <li>不同类型使用不同颜色：错误(红)、警告(橙)、信息(蓝)、成功(绿)</li>
                <li>带有图标，增强视觉识别</li>
                <li>阴影效果，增加层次感</li>
            </ul>
            
            <h4>交互设计：</h4>
            <ul>
                <li>支持多行文本显示，格式清晰</li>
                <li>错误类型需要用户确认关闭</li>
                <li>其他类型自动消失，不打扰用户</li>
                <li>响应式设计，适配移动设备</li>
            </ul>
        </div>
    </div>

    <div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <strong>🎉 升级完成！</strong>
        <p>现在所有的验证和反馈信息都会在前端界面上以美观的方式显示，用户体验大大提升！</p>
    </div>
</body>
</html>
